{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\SubtleParticles.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SubtleParticles = ({\n  children,\n  particleCount = 80,\n  color = '#007AFF',\n  opacity = 0.4\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    let animationId;\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Floating particles\n    const particles = [];\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.vx = (Math.random() - 0.5) * 0.3;\n        this.vy = (Math.random() - 0.5) * 0.3;\n        this.size = Math.random() * 3 + 1;\n        this.opacity = Math.random() * 0.6 + 0.2;\n        this.life = Math.random() * 300 + 150;\n        this.maxLife = this.life;\n      }\n      update() {\n        this.x += this.vx;\n        this.y += this.vy;\n        this.life--;\n\n        // Fade out as life decreases\n        this.opacity = this.life / this.maxLife * 0.3;\n\n        // Respawn when life ends\n        if (this.life <= 0) {\n          this.x = Math.random() * canvas.width;\n          this.y = Math.random() * canvas.height;\n          this.life = this.maxLife;\n          this.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Wrap around edges\n        if (this.x < 0) this.x = canvas.width;\n        if (this.x > canvas.width) this.x = 0;\n        if (this.y < 0) this.y = canvas.height;\n        if (this.y > canvas.height) this.y = 0;\n      }\n      draw() {\n        ctx.save();\n        ctx.globalAlpha = this.opacity * opacity;\n        ctx.fillStyle = color;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.restore();\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push(new Particle());\n    }\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      // Draw subtle connections between nearby particles\n      ctx.strokeStyle = `${color}${Math.floor(opacity * 50).toString(16).padStart(2, '0')}`;\n      ctx.lineWidth = 0.5;\n      for (let i = 0; i < particles.length; i++) {\n        for (let j = i + 1; j < particles.length; j++) {\n          const dx = particles[i].x - particles[j].x;\n          const dy = particles[i].y - particles[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          if (distance < 100) {\n            const alpha = (100 - distance) / 100 * opacity * 0.3;\n            ctx.globalAlpha = alpha;\n            ctx.beginPath();\n            ctx.moveTo(particles[i].x, particles[i].y);\n            ctx.lineTo(particles[j].x, particles[j].y);\n            ctx.stroke();\n          }\n        }\n      }\n      ctx.globalAlpha = 1;\n      animationId = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [particleCount, color, opacity]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: -1,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(SubtleParticles, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = SubtleParticles;\nexport default SubtleParticles;\nvar _c;\n$RefreshReg$(_c, \"SubtleParticles\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "jsxDEV", "_jsxDEV", "SubtleParticles", "children", "particleCount", "color", "opacity", "_s", "canvasRef", "canvas", "current", "ctx", "getContext", "animationId", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "particles", "Particle", "constructor", "x", "Math", "random", "y", "vx", "vy", "size", "life", "maxLife", "update", "draw", "save", "globalAlpha", "fillStyle", "beginPath", "arc", "PI", "fill", "restore", "i", "push", "animate", "clearRect", "for<PERSON>ach", "particle", "strokeStyle", "floor", "toString", "padStart", "lineWidth", "length", "j", "dx", "dy", "distance", "sqrt", "alpha", "moveTo", "lineTo", "stroke", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "sx", "position", "ref", "style", "top", "left", "zIndex", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/SubtleParticles.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\n\nconst SubtleParticles = ({ children, particleCount = 80, color = '#007AFF', opacity = 0.4 }) => {\n  const canvasRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationId;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Floating particles\n    const particles = [];\n\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.vx = (Math.random() - 0.5) * 0.3;\n        this.vy = (Math.random() - 0.5) * 0.3;\n        this.size = Math.random() * 3 + 1;\n        this.opacity = Math.random() * 0.6 + 0.2;\n        this.life = Math.random() * 300 + 150;\n        this.maxLife = this.life;\n      }\n\n      update() {\n        this.x += this.vx;\n        this.y += this.vy;\n        this.life--;\n\n        // Fade out as life decreases\n        this.opacity = (this.life / this.maxLife) * 0.3;\n\n        // Respawn when life ends\n        if (this.life <= 0) {\n          this.x = Math.random() * canvas.width;\n          this.y = Math.random() * canvas.height;\n          this.life = this.maxLife;\n          this.opacity = Math.random() * 0.3 + 0.1;\n        }\n\n        // Wrap around edges\n        if (this.x < 0) this.x = canvas.width;\n        if (this.x > canvas.width) this.x = 0;\n        if (this.y < 0) this.y = canvas.height;\n        if (this.y > canvas.height) this.y = 0;\n      }\n\n      draw() {\n        ctx.save();\n        ctx.globalAlpha = this.opacity * opacity;\n        ctx.fillStyle = color;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.restore();\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push(new Particle());\n    }\n\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      // Draw subtle connections between nearby particles\n      ctx.strokeStyle = `${color}${Math.floor(opacity * 50).toString(16).padStart(2, '0')}`;\n      ctx.lineWidth = 0.5;\n\n      for (let i = 0; i < particles.length; i++) {\n        for (let j = i + 1; j < particles.length; j++) {\n          const dx = particles[i].x - particles[j].x;\n          const dy = particles[i].y - particles[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < 100) {\n            const alpha = (100 - distance) / 100 * opacity * 0.3;\n            ctx.globalAlpha = alpha;\n            ctx.beginPath();\n            ctx.moveTo(particles[i].x, particles[i].y);\n            ctx.lineTo(particles[j].x, particles[j].y);\n            ctx.stroke();\n          }\n        }\n      }\n\n      ctx.globalAlpha = 1;\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [particleCount, color, opacity]);\n\n  return (\n    <Box sx={{ position: 'relative' }}>\n      <canvas\n        ref={canvasRef}\n        style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          zIndex: -1,\n          pointerEvents: 'none',\n        }}\n      />\n      {children}\n    </Box>\n  );\n};\n\nexport default SubtleParticles;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG,EAAE;EAAEC,KAAK,GAAG,SAAS;EAAEC,OAAO,GAAG;AAAI,CAAC,KAAK;EAAAC,EAAA;EAC9F,MAAMC,SAAS,GAAGV,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,MAAMY,MAAM,GAAGD,SAAS,CAACE,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAIC,WAAW;IAEf,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBL,MAAM,CAACM,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCR,MAAM,CAACS,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,SAAS,GAAG,EAAE;IAEpB,MAAMC,QAAQ,CAAC;MACbC,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGjB,MAAM,CAACM,KAAK;QACrC,IAAI,CAACY,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGjB,MAAM,CAACS,MAAM;QACtC,IAAI,CAACU,EAAE,GAAG,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACrC,IAAI,CAACG,EAAE,GAAG,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACrC,IAAI,CAACI,IAAI,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACjC,IAAI,CAACpB,OAAO,GAAGmB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QACxC,IAAI,CAACK,IAAI,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QACrC,IAAI,CAACM,OAAO,GAAG,IAAI,CAACD,IAAI;MAC1B;MAEAE,MAAMA,CAAA,EAAG;QACP,IAAI,CAACT,CAAC,IAAI,IAAI,CAACI,EAAE;QACjB,IAAI,CAACD,CAAC,IAAI,IAAI,CAACE,EAAE;QACjB,IAAI,CAACE,IAAI,EAAE;;QAEX;QACA,IAAI,CAACzB,OAAO,GAAI,IAAI,CAACyB,IAAI,GAAG,IAAI,CAACC,OAAO,GAAI,GAAG;;QAE/C;QACA,IAAI,IAAI,CAACD,IAAI,IAAI,CAAC,EAAE;UAClB,IAAI,CAACP,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGjB,MAAM,CAACM,KAAK;UACrC,IAAI,CAACY,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGjB,MAAM,CAACS,MAAM;UACtC,IAAI,CAACa,IAAI,GAAG,IAAI,CAACC,OAAO;UACxB,IAAI,CAAC1B,OAAO,GAAGmB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAC1C;;QAEA;QACA,IAAI,IAAI,CAACF,CAAC,GAAG,CAAC,EAAE,IAAI,CAACA,CAAC,GAAGf,MAAM,CAACM,KAAK;QACrC,IAAI,IAAI,CAACS,CAAC,GAAGf,MAAM,CAACM,KAAK,EAAE,IAAI,CAACS,CAAC,GAAG,CAAC;QACrC,IAAI,IAAI,CAACG,CAAC,GAAG,CAAC,EAAE,IAAI,CAACA,CAAC,GAAGlB,MAAM,CAACS,MAAM;QACtC,IAAI,IAAI,CAACS,CAAC,GAAGlB,MAAM,CAACS,MAAM,EAAE,IAAI,CAACS,CAAC,GAAG,CAAC;MACxC;MAEAO,IAAIA,CAAA,EAAG;QACLvB,GAAG,CAACwB,IAAI,CAAC,CAAC;QACVxB,GAAG,CAACyB,WAAW,GAAG,IAAI,CAAC9B,OAAO,GAAGA,OAAO;QACxCK,GAAG,CAAC0B,SAAS,GAAGhC,KAAK;QACrBM,GAAG,CAAC2B,SAAS,CAAC,CAAC;QACf3B,GAAG,CAAC4B,GAAG,CAAC,IAAI,CAACf,CAAC,EAAE,IAAI,CAACG,CAAC,EAAE,IAAI,CAACG,IAAI,EAAE,CAAC,EAAEL,IAAI,CAACe,EAAE,GAAG,CAAC,CAAC;QAClD7B,GAAG,CAAC8B,IAAI,CAAC,CAAC;QACV9B,GAAG,CAAC+B,OAAO,CAAC,CAAC;MACf;IACF;;IAEA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,aAAa,EAAEuC,CAAC,EAAE,EAAE;MACtCtB,SAAS,CAACuB,IAAI,CAAC,IAAItB,QAAQ,CAAC,CAAC,CAAC;IAChC;IAEA,MAAMuB,OAAO,GAAGA,CAAA,KAAM;MACpBlC,GAAG,CAACmC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAErC,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACS,MAAM,CAAC;MAEhDG,SAAS,CAAC0B,OAAO,CAACC,QAAQ,IAAI;QAC5BA,QAAQ,CAACf,MAAM,CAAC,CAAC;QACjBe,QAAQ,CAACd,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;;MAEF;MACAvB,GAAG,CAACsC,WAAW,GAAG,GAAG5C,KAAK,GAAGoB,IAAI,CAACyB,KAAK,CAAC5C,OAAO,GAAG,EAAE,CAAC,CAAC6C,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACrFzC,GAAG,CAAC0C,SAAS,GAAG,GAAG;MAEnB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,SAAS,CAACiC,MAAM,EAAEX,CAAC,EAAE,EAAE;QACzC,KAAK,IAAIY,CAAC,GAAGZ,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAGlC,SAAS,CAACiC,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC7C,MAAMC,EAAE,GAAGnC,SAAS,CAACsB,CAAC,CAAC,CAACnB,CAAC,GAAGH,SAAS,CAACkC,CAAC,CAAC,CAAC/B,CAAC;UAC1C,MAAMiC,EAAE,GAAGpC,SAAS,CAACsB,CAAC,CAAC,CAAChB,CAAC,GAAGN,SAAS,CAACkC,CAAC,CAAC,CAAC5B,CAAC;UAC1C,MAAM+B,QAAQ,GAAGjC,IAAI,CAACkC,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;UAE7C,IAAIC,QAAQ,GAAG,GAAG,EAAE;YAClB,MAAME,KAAK,GAAG,CAAC,GAAG,GAAGF,QAAQ,IAAI,GAAG,GAAGpD,OAAO,GAAG,GAAG;YACpDK,GAAG,CAACyB,WAAW,GAAGwB,KAAK;YACvBjD,GAAG,CAAC2B,SAAS,CAAC,CAAC;YACf3B,GAAG,CAACkD,MAAM,CAACxC,SAAS,CAACsB,CAAC,CAAC,CAACnB,CAAC,EAAEH,SAAS,CAACsB,CAAC,CAAC,CAAChB,CAAC,CAAC;YAC1ChB,GAAG,CAACmD,MAAM,CAACzC,SAAS,CAACkC,CAAC,CAAC,CAAC/B,CAAC,EAAEH,SAAS,CAACkC,CAAC,CAAC,CAAC5B,CAAC,CAAC;YAC1ChB,GAAG,CAACoD,MAAM,CAAC,CAAC;UACd;QACF;MACF;MAEApD,GAAG,CAACyB,WAAW,GAAG,CAAC;MACnBvB,WAAW,GAAGmD,qBAAqB,CAACnB,OAAO,CAAC;IAC9C,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACX7B,MAAM,CAACiD,mBAAmB,CAAC,QAAQ,EAAEnD,YAAY,CAAC;MAClD,IAAID,WAAW,EAAE;QACfqD,oBAAoB,CAACrD,WAAW,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAACT,aAAa,EAAEC,KAAK,EAAEC,OAAO,CAAC,CAAC;EAEnC,oBACEL,OAAA,CAACF,GAAG;IAACoE,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAW,CAAE;IAAAjE,QAAA,gBAChCF,OAAA;MACEoE,GAAG,EAAE7D,SAAU;MACf8D,KAAK,EAAE;QACLF,QAAQ,EAAE,OAAO;QACjBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPzD,KAAK,EAAE,MAAM;QACbG,MAAM,EAAE,MAAM;QACduD,MAAM,EAAE,CAAC,CAAC;QACVC,aAAa,EAAE;MACjB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACD3E,QAAQ;EAAA;IAAAwE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvE,EAAA,CApIIL,eAAe;AAAA6E,EAAA,GAAf7E,eAAe;AAsIrB,eAAeA,eAAe;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}