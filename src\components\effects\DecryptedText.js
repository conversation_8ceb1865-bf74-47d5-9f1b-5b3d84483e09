import React, { useState, useEffect } from 'react';
import { Typography } from '@mui/material';

const DecryptedText = ({ 
  text, 
  variant = 'h4',
  className = '',
  sx = {},
  speed = 30,
  characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
  ...props 
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isDecrypting, setIsDecrypting] = useState(false);

  useEffect(() => {
    if (!text) return;

    let timeouts = [];
    setIsDecrypting(true);
    setDisplayText('');

    // Initial scrambled text
    const scrambledText = text
      .split('')
      .map(char => char === ' ' ? ' ' : characters[Math.floor(Math.random() * characters.length)])
      .join('');
    
    setDisplayText(scrambledText);

    // Decrypt each character one by one
    text.split('').forEach((char, index) => {
      const timeout = setTimeout(() => {
        setDisplayText(prev => {
          const newText = prev.split('');
          
          // Scramble remaining characters
          for (let i = index + 1; i < newText.length; i++) {
            if (text[i] !== ' ') {
              newText[i] = characters[Math.floor(Math.random() * characters.length)];
            }
          }
          
          // Set the correct character
          newText[index] = char;
          
          return newText.join('');
        });

        if (index === text.length - 1) {
          setIsDecrypting(false);
        }
      }, index * speed);

      timeouts.push(timeout);
    });

    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [text, speed, characters]);

  return (
    <Typography
      variant={variant}
      className={`${className} ${isDecrypting ? 'decrypting' : 'decrypted'}`}
      sx={{
        fontFamily: 'monospace',
        letterSpacing: '0.1em',
        position: 'relative',
        '&.decrypting': {
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(90deg, transparent 0%, rgba(0, 122, 255, 0.1) 50%, transparent 100%)',
            animation: 'decrypt-scan 1.5s ease-in-out',
            pointerEvents: 'none',
          }
        },
        '&.decrypted': {
          textShadow: '0 0 10px rgba(0, 122, 255, 0.5)',
        },
        '@keyframes decrypt-scan': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' }
        },
        ...sx
      }}
      {...props}
    >
      {displayText}
    </Typography>
  );
};

export default DecryptedText;
