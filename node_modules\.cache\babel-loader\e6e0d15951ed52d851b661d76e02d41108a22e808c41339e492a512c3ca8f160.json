{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\GlowCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Paper, Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GlowCard = ({\n  children,\n  glowColor = '#007AFF',\n  intensity = 0.5,\n  sx = {},\n  ...props\n}) => {\n  _s();\n  const [isHovered, setIsHovered] = useState(false);\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    ...props,\n    onMouseEnter: () => setIsHovered(true),\n    onMouseLeave: () => setIsHovered(false),\n    sx: {\n      position: 'relative',\n      overflow: 'hidden',\n      transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n      transform: isHovered ? 'translateY(-8px)' : 'translateY(0)',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: `linear-gradient(135deg, ${glowColor}20 0%, transparent 50%, ${glowColor}10 100%)`,\n        opacity: isHovered ? intensity : intensity * 0.3,\n        transition: 'opacity 0.3s ease',\n        pointerEvents: 'none',\n        zIndex: 0\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        top: -2,\n        left: -2,\n        right: -2,\n        bottom: -2,\n        background: `linear-gradient(45deg, ${glowColor}60, transparent, ${glowColor}60)`,\n        borderRadius: 'inherit',\n        opacity: isHovered ? 0.6 : 0,\n        transition: 'opacity 0.3s ease',\n        pointerEvents: 'none',\n        zIndex: -1,\n        filter: 'blur(8px)'\n      },\n      boxShadow: isHovered ? `0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px ${glowColor}40` : '0 8px 32px rgba(0, 0, 0, 0.2)',\n      ...sx\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(GlowCard, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n_c = GlowCard;\nexport default GlowCard;\nvar _c;\n$RefreshReg$(_c, \"GlowCard\");", "map": {"version": 3, "names": ["React", "useState", "Paper", "Box", "jsxDEV", "_jsxDEV", "GlowCard", "children", "glowColor", "intensity", "sx", "props", "_s", "isHovered", "setIsHovered", "onMouseEnter", "onMouseLeave", "position", "overflow", "transition", "transform", "content", "top", "left", "right", "bottom", "background", "opacity", "pointerEvents", "zIndex", "borderRadius", "filter", "boxShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/GlowCard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Paper, Box } from '@mui/material';\n\nconst GlowCard = ({ \n  children, \n  glowColor = '#007AFF', \n  intensity = 0.5,\n  sx = {},\n  ...props \n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <Paper\n      {...props}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n      sx={{\n        position: 'relative',\n        overflow: 'hidden',\n        transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n        transform: isHovered ? 'translateY(-8px)' : 'translateY(0)',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: `linear-gradient(135deg, ${glowColor}20 0%, transparent 50%, ${glowColor}10 100%)`,\n          opacity: isHovered ? intensity : intensity * 0.3,\n          transition: 'opacity 0.3s ease',\n          pointerEvents: 'none',\n          zIndex: 0,\n        },\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          top: -2,\n          left: -2,\n          right: -2,\n          bottom: -2,\n          background: `linear-gradient(45deg, ${glowColor}60, transparent, ${glowColor}60)`,\n          borderRadius: 'inherit',\n          opacity: isHovered ? 0.6 : 0,\n          transition: 'opacity 0.3s ease',\n          pointerEvents: 'none',\n          zIndex: -1,\n          filter: 'blur(8px)',\n        },\n        boxShadow: isHovered \n          ? `0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px ${glowColor}40`\n          : '0 8px 32px rgba(0, 0, 0, 0.2)',\n        ...sx\n      }}\n    >\n      <Box sx={{ position: 'relative', zIndex: 1 }}>\n        {children}\n      </Box>\n    </Paper>\n  );\n};\n\nexport default GlowCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,EAAEC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,QAAQ,GAAGA,CAAC;EAChBC,QAAQ;EACRC,SAAS,GAAG,SAAS;EACrBC,SAAS,GAAG,GAAG;EACfC,EAAE,GAAG,CAAC,CAAC;EACP,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAEjD,oBACEI,OAAA,CAACH,KAAK;IAAA,GACAS,KAAK;IACTI,YAAY,EAAEA,CAAA,KAAMD,YAAY,CAAC,IAAI,CAAE;IACvCE,YAAY,EAAEA,CAAA,KAAMF,YAAY,CAAC,KAAK,CAAE;IACxCJ,EAAE,EAAE;MACFO,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,+CAA+C;MAC3DC,SAAS,EAAEP,SAAS,GAAG,kBAAkB,GAAG,eAAe;MAC3D,WAAW,EAAE;QACXQ,OAAO,EAAE,IAAI;QACbJ,QAAQ,EAAE,UAAU;QACpBK,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,UAAU,EAAE,2BAA2BlB,SAAS,2BAA2BA,SAAS,UAAU;QAC9FmB,OAAO,EAAEd,SAAS,GAAGJ,SAAS,GAAGA,SAAS,GAAG,GAAG;QAChDU,UAAU,EAAE,mBAAmB;QAC/BS,aAAa,EAAE,MAAM;QACrBC,MAAM,EAAE;MACV,CAAC;MACD,UAAU,EAAE;QACVR,OAAO,EAAE,IAAI;QACbJ,QAAQ,EAAE,UAAU;QACpBK,GAAG,EAAE,CAAC,CAAC;QACPC,IAAI,EAAE,CAAC,CAAC;QACRC,KAAK,EAAE,CAAC,CAAC;QACTC,MAAM,EAAE,CAAC,CAAC;QACVC,UAAU,EAAE,0BAA0BlB,SAAS,oBAAoBA,SAAS,KAAK;QACjFsB,YAAY,EAAE,SAAS;QACvBH,OAAO,EAAEd,SAAS,GAAG,GAAG,GAAG,CAAC;QAC5BM,UAAU,EAAE,mBAAmB;QAC/BS,aAAa,EAAE,MAAM;QACrBC,MAAM,EAAE,CAAC,CAAC;QACVE,MAAM,EAAE;MACV,CAAC;MACDC,SAAS,EAAEnB,SAAS,GAChB,4CAA4CL,SAAS,IAAI,GACzD,+BAA+B;MACnC,GAAGE;IACL,CAAE;IAAAH,QAAA,eAEFF,OAAA,CAACF,GAAG;MAACO,EAAE,EAAE;QAAEO,QAAQ,EAAE,UAAU;QAAEY,MAAM,EAAE;MAAE,CAAE;MAAAtB,QAAA,EAC1CA;IAAQ;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACxB,EAAA,CA1DIN,QAAQ;AAAA+B,EAAA,GAAR/B,QAAQ;AA4Dd,eAAeA,QAAQ;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}