{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\Projects.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Typography, Grid, Paper, Box, Chip, Link, Tabs, Tab } from '@mui/material';\nimport GitHubIcon from '@mui/icons-material/GitHub';\nimport LaunchIcon from '@mui/icons-material/Launch';\nimport ProjectShowcase from './ProjectShowcase';\nimport InteractiveTimeline from './InteractiveTimeline';\nimport { DecryptedText } from './effects';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Projects() {\n  _s();\n  const [hoveredIndex, setHoveredIndex] = useState(null);\n  const [activeTab, setActiveTab] = useState(0);\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const projects = [{\n    title: \"MarketAnalyzerX\",\n    description: \"Developed MarketAnalyzerX to analyze stock charts using OCR and image processing with integrated MediaStack API for real-time financial headlines.\",\n    tech: [\"Streamlit\", \"Python\", \"API\", \"Agentic ML\", \"Computer Vision\"],\n    image: \"marketanalyzerx.streamlit.app/\",\n    github: \"https://github.com/joelprince2601/MarketAnalyzerX\",\n    features: [\"Analyze stock charts using OCR and image processing\", \"Integrated MediaStack API for real-time financial headlines\", \"Enabled drawing tool and screenshot analysis for pattern recognition\", \"Live demo: marketanalyzerx.streamlit.app\"]\n  }, {\n    title: \"Autonomous Tactical Navigation System (ATNS)\",\n    description: \"Built a fully autonomous navigation system for unmanned vehicles in hostile environments with 94% accurate real-time threat detection.\",\n    tech: [\"Computer Vision\", \"DeepSort\", \"Edge Computing\", \"YOLO\"],\n    image: \"url_to_atns_image\",\n    github: \"https://github.com/joelprince2601/ATNS\",\n    features: [\"Fully autonomous navigation system for unmanned vehicles\", \"Used YOLO and DeepSort for 94% accurate real-time threat detection\", \"Developed terrain-aware pathfinding with sensor fusion\", \"Edge AI for rapid decision-making in hostile environments\"]\n  }, {\n    title: \"Reinforcement Learning DDoS Defense\",\n    description: \"Built a Q-Learning RL model for real-time DDoS defense and resource optimization with live graph visualizations.\",\n    tech: [\"Python\", \"Q-Learning\", \"Matplotlib\", \"NumPy\", \"OpenAI Gym\"],\n    image: \"url_to_rl_ddos_image\",\n    github: \"https://github.com/joelprince2601/RL-DDoS-Defense\",\n    features: [\"Q-Learning RL model for real-time DDoS defense and resource optimization\", \"Simulated attacks with live graph visualizations of intensity and defense\", \"Tracked agent performance through dynamic bar/line charts during training\", \"Real-time monitoring of agent rewards and defense effectiveness\"]\n  }, {\n    title: \"Reinforcement Learning Cloud\",\n    description: \"AI-driven reinforcement learning system for optimizing cloud security, resource management, and performance.\",\n    tech: [\"Python\", \"Streamlit\", \"Reinforcement Learning\", \"Cloud\"],\n    image: \"url_to_rlcloud_image\",\n    github: \"https://github.com/joelprince2601/Reinforcement-Learning-Cloud\",\n    features: [\"Cloud resource optimization using RL\", \"Security enhancement via AI agents\", \"Scalable cloud performance monitoring\", \"Live demo: jpcloud-rl.streamlit.app\"]\n  }, {\n    title: \"AML Detection\",\n    description: \"Transaction analysis system that detects anomalies in sender, recipient, or transaction details using uploaded Excel data.\",\n    tech: [\"Python\", \"Pandas\", \"Streamlit\", \"Excel\"],\n    image: \"url_to_aml_image\",\n    github: \"https://github.com/joelprince2601/AML_Detection\",\n    features: [\"Excel-based transaction data analysis\", \"Suspicious activity detection\", \"Upload or use sample datasets\", \"User-friendly web interface\"]\n  }, {\n    title: \"Gesture Controller\",\n    description: \"Adaptive hand gesture recognition system that enables individuals with mobility limitations to control their computers.\",\n    tech: [\"Python\", \"OpenCV\", \"Mediapipe\", \"Machine Learning\"],\n    image: \"url_to_gesture_image\",\n    github: \"https://github.com/joelprince2601/GestureController\",\n    features: [\"Custom gesture recognition\", \"Hands-free computer control\", \"Accessibility-focused design\", \"Real-time camera input processing\"]\n  }, {\n    title: \"Real-Time Traffic Monitoring\",\n    description: \"Video processing system using YOLOv4 and DeepSort for traffic management\",\n    tech: [\"OpenCV\", \"YOLOv4\", \"DeepSort\", \"Python\"],\n    image: \"url_to_traffic_image\",\n    github: \"https://github.com/joelprince2601/\",\n    features: [\"Object detection & tracking\", \"Dynamic traffic control\", \"Real-time analytics\", \"Grid overlay system\"]\n  }, {\n    title: \"Cybersecurity Toolkit\",\n    description: \"Comprehensive suite of security testing and analysis tools\",\n    tech: [\"Python\", \"Pentesting\", \"Network\", \"Security\"],\n    image: \"url_to_security_image\",\n    github: \"https://github.com/joelprince2601/\",\n    features: [\"Vulnerability scanning\", \"Network analysis\", \"Security assessment\", \"Automated reporting\"]\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(DecryptedText, {\n      text: \"Featured Projects\",\n      variant: \"h4\",\n      className: \"cyber-text gradient-text\",\n      sx: {\n        mb: 4,\n        textAlign: 'center'\n      },\n      speed: 80\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider',\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        centered: true,\n        sx: {\n          '& .MuiTab-root': {\n            color: 'text.secondary',\n            '&.Mui-selected': {\n              color: 'primary.main'\n            }\n          },\n          '& .MuiTabs-indicator': {\n            background: 'linear-gradient(45deg, #6366f1, #8b5cf6)',\n            height: 3\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"All Projects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Featured Showcase\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Journey Timeline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      children: projects.map((project, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          className: \"glass-effect\",\n          sx: {\n            p: 3,\n            height: '100%',\n            position: 'relative',\n            transform: hoveredIndex === index ? 'translateZ(50px)' : 'none',\n            transition: 'all 0.3s ease-out',\n            '&:hover': {\n              transform: 'scale(1.02) translateZ(50px) rotateX(2deg)'\n            },\n            perspective: '1000px'\n          },\n          onMouseEnter: () => setHoveredIndex(index),\n          onMouseLeave: () => setHoveredIndex(null),\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: `linear-gradient(135deg, \n                    rgba(156, 39, 176, 0.1) 0%, \n                    rgba(245, 0, 87, 0.1) 100%)`,\n              opacity: hoveredIndex === index ? 1 : 0,\n              transition: 'opacity 0.3s ease',\n              zIndex: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'relative',\n              zIndex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(DecryptedText, {\n              text: project.title,\n              variant: \"h5\",\n              className: \"glow-text\",\n              sx: {\n                mb: 2\n              },\n              speed: 60\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 3,\n                minHeight: '60px'\n              },\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3\n              },\n              children: project.tech.map((tech, idx) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: tech,\n                sx: {\n                  m: 0.5,\n                  background: 'linear-gradient(45deg, #9c27b0, #f50057)',\n                  color: 'white',\n                  '&:hover': {\n                    transform: 'scale(1.1)'\n                  },\n                  transition: 'transform 0.2s ease'\n                }\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                className: \"animated-underline\",\n                children: \"Key Features:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), project.features.map((feature, idx) => /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  '&::before': {\n                    content: '\"\"',\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    background: 'linear-gradient(45deg, #9c27b0, #f50057)',\n                    marginRight: '8px'\n                  }\n                },\n                children: feature\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                mt: 3,\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                href: project.github,\n                target: \"_blank\",\n                sx: {\n                  color: 'inherit',\n                  '&:hover': {\n                    color: '#9c27b0'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(GitHubIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                href: \"#\",\n                target: \"_blank\",\n                sx: {\n                  color: 'inherit',\n                  '&:hover': {\n                    color: '#f50057'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(LaunchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(ProjectShowcase, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 9\n    }, this), activeTab === 2 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(InteractiveTimeline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n}\n_s(Projects, \"yg3eqPtVDag1SYvcVtflDdIOnLA=\");\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Typography", "Grid", "Paper", "Box", "Chip", "Link", "Tabs", "Tab", "GitHubIcon", "LaunchIcon", "ProjectShowcase", "InteractiveTimeline", "DecryptedText", "jsxDEV", "_jsxDEV", "Projects", "_s", "hoveredIndex", "setHoveredIndex", "activeTab", "setActiveTab", "handleTabChange", "event", "newValue", "projects", "title", "description", "tech", "image", "github", "features", "max<PERSON><PERSON><PERSON>", "sx", "mt", "children", "text", "variant", "className", "mb", "textAlign", "speed", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderBottom", "borderColor", "value", "onChange", "centered", "color", "background", "height", "label", "container", "spacing", "map", "project", "index", "item", "xs", "md", "p", "position", "transform", "transition", "perspective", "onMouseEnter", "onMouseLeave", "top", "left", "right", "bottom", "opacity", "zIndex", "minHeight", "idx", "m", "display", "flexDirection", "gap", "feature", "alignItems", "content", "width", "borderRadius", "marginRight", "justifyContent", "href", "target", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/Projects.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Container, Typography, Grid, Paper, Box, Chip, Link, Tabs, Tab } from '@mui/material';\r\nimport GitHubIcon from '@mui/icons-material/GitHub';\r\nimport LaunchIcon from '@mui/icons-material/Launch';\r\nimport ProjectShowcase from './ProjectShowcase';\r\nimport InteractiveTimeline from './InteractiveTimeline';\r\nimport { DecryptedText } from './effects';\r\n\r\nfunction Projects() {\r\n  const [hoveredIndex, setHoveredIndex] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(0);\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n  };\r\n\r\n  const projects = [\r\n    {\r\n      title: \"MarketAnalyzerX\",\r\n      description: \"Developed MarketAnalyzerX to analyze stock charts using OCR and image processing with integrated MediaStack API for real-time financial headlines.\",\r\n      tech: [\"Streamlit\", \"Python\", \"API\", \"Agentic ML\", \"Computer Vision\"],\r\n      image: \"marketanalyzerx.streamlit.app/\",\r\n      github: \"https://github.com/joelprince2601/MarketAnalyzerX\",\r\n      features: [\r\n        \"Analyze stock charts using OCR and image processing\",\r\n        \"Integrated MediaStack API for real-time financial headlines\",\r\n        \"Enabled drawing tool and screenshot analysis for pattern recognition\",\r\n        \"Live demo: marketanalyzerx.streamlit.app\"\r\n      ]\r\n    },\r\n    {\r\n      title: \"Autonomous Tactical Navigation System (ATNS)\",\r\n      description: \"Built a fully autonomous navigation system for unmanned vehicles in hostile environments with 94% accurate real-time threat detection.\",\r\n      tech: [\"Computer Vision\", \"DeepSort\", \"Edge Computing\", \"YOLO\"],\r\n      image: \"url_to_atns_image\",\r\n      github: \"https://github.com/joelprince2601/ATNS\",\r\n      features: [\r\n        \"Fully autonomous navigation system for unmanned vehicles\",\r\n        \"Used YOLO and DeepSort for 94% accurate real-time threat detection\",\r\n        \"Developed terrain-aware pathfinding with sensor fusion\",\r\n        \"Edge AI for rapid decision-making in hostile environments\"\r\n      ]\r\n    },\r\n    {\r\n      title: \"Reinforcement Learning DDoS Defense\",\r\n      description: \"Built a Q-Learning RL model for real-time DDoS defense and resource optimization with live graph visualizations.\",\r\n      tech: [\"Python\", \"Q-Learning\", \"Matplotlib\", \"NumPy\", \"OpenAI Gym\"],\r\n      image: \"url_to_rl_ddos_image\",\r\n      github: \"https://github.com/joelprince2601/RL-DDoS-Defense\",\r\n      features: [\r\n        \"Q-Learning RL model for real-time DDoS defense and resource optimization\",\r\n        \"Simulated attacks with live graph visualizations of intensity and defense\",\r\n        \"Tracked agent performance through dynamic bar/line charts during training\",\r\n        \"Real-time monitoring of agent rewards and defense effectiveness\"\r\n      ]\r\n    },\r\n    {\r\n      title: \"Reinforcement Learning Cloud\",\r\n      description: \"AI-driven reinforcement learning system for optimizing cloud security, resource management, and performance.\",\r\n      tech: [\"Python\", \"Streamlit\", \"Reinforcement Learning\", \"Cloud\"],\r\n      image: \"url_to_rlcloud_image\",\r\n      github: \"https://github.com/joelprince2601/Reinforcement-Learning-Cloud\",\r\n      features: [\r\n        \"Cloud resource optimization using RL\",\r\n        \"Security enhancement via AI agents\",\r\n        \"Scalable cloud performance monitoring\",\r\n        \"Live demo: jpcloud-rl.streamlit.app\"\r\n      ]\r\n    },\r\n    {\r\n      title: \"AML Detection\",\r\n      description: \"Transaction analysis system that detects anomalies in sender, recipient, or transaction details using uploaded Excel data.\",\r\n      tech: [\"Python\", \"Pandas\", \"Streamlit\", \"Excel\"],\r\n      image: \"url_to_aml_image\",\r\n      github: \"https://github.com/joelprince2601/AML_Detection\",\r\n      features: [\r\n        \"Excel-based transaction data analysis\",\r\n        \"Suspicious activity detection\",\r\n        \"Upload or use sample datasets\",\r\n        \"User-friendly web interface\"\r\n      ]\r\n    },\r\n    {\r\n      title: \"Gesture Controller\",\r\n      description: \"Adaptive hand gesture recognition system that enables individuals with mobility limitations to control their computers.\",\r\n      tech: [\"Python\", \"OpenCV\", \"Mediapipe\", \"Machine Learning\"],\r\n      image: \"url_to_gesture_image\",\r\n      github: \"https://github.com/joelprince2601/GestureController\",\r\n      features: [\r\n        \"Custom gesture recognition\",\r\n        \"Hands-free computer control\",\r\n        \"Accessibility-focused design\",\r\n        \"Real-time camera input processing\"\r\n      ]\r\n    },\r\n    {\r\n      title: \"Real-Time Traffic Monitoring\",\r\n      description: \"Video processing system using YOLOv4 and DeepSort for traffic management\",\r\n      tech: [\"OpenCV\", \"YOLOv4\", \"DeepSort\", \"Python\"],\r\n      image: \"url_to_traffic_image\",\r\n      github: \"https://github.com/joelprince2601/\",\r\n      features: [\r\n        \"Object detection & tracking\",\r\n        \"Dynamic traffic control\",\r\n        \"Real-time analytics\",\r\n        \"Grid overlay system\"\r\n      ]\r\n    },\r\n    {\r\n      title: \"Cybersecurity Toolkit\",\r\n      description: \"Comprehensive suite of security testing and analysis tools\",\r\n      tech: [\"Python\", \"Pentesting\", \"Network\", \"Security\"],\r\n      image: \"url_to_security_image\",\r\n      github: \"https://github.com/joelprince2601/\",\r\n      features: [\r\n        \"Vulnerability scanning\",\r\n        \"Network analysis\",\r\n        \"Security assessment\",\r\n        \"Automated reporting\"\r\n      ]\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\" sx={{ mt: 4 }}>\r\n      <DecryptedText\r\n        text=\"Featured Projects\"\r\n        variant=\"h4\"\r\n        className=\"cyber-text gradient-text\"\r\n        sx={{ mb: 4, textAlign: 'center' }}\r\n        speed={80}\r\n      />\r\n\r\n      {/* Tabs */}\r\n      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\r\n        <Tabs\r\n          value={activeTab}\r\n          onChange={handleTabChange}\r\n          centered\r\n          sx={{\r\n            '& .MuiTab-root': {\r\n              color: 'text.secondary',\r\n              '&.Mui-selected': {\r\n                color: 'primary.main',\r\n              },\r\n            },\r\n            '& .MuiTabs-indicator': {\r\n              background: 'linear-gradient(45deg, #6366f1, #8b5cf6)',\r\n              height: 3,\r\n            },\r\n          }}\r\n        >\r\n          <Tab label=\"All Projects\" />\r\n          <Tab label=\"Featured Showcase\" />\r\n          <Tab label=\"Journey Timeline\" />\r\n        </Tabs>\r\n      </Box>\r\n\r\n      {/* Tab Panels */}\r\n      {activeTab === 0 && (\r\n        <Grid container spacing={4}>\r\n          {projects.map((project, index) => (\r\n          <Grid item xs={12} md={6} key={index}>\r\n            <Paper\r\n              className=\"glass-effect\"\r\n              sx={{\r\n                p: 3,\r\n                height: '100%',\r\n                position: 'relative',\r\n                transform: hoveredIndex === index ? 'translateZ(50px)' : 'none',\r\n                transition: 'all 0.3s ease-out',\r\n                '&:hover': {\r\n                  transform: 'scale(1.02) translateZ(50px) rotateX(2deg)',\r\n                },\r\n                perspective: '1000px',\r\n              }}\r\n              onMouseEnter={() => setHoveredIndex(index)}\r\n              onMouseLeave={() => setHoveredIndex(null)}\r\n            >\r\n              <Box\r\n                sx={{\r\n                  position: 'absolute',\r\n                  top: 0,\r\n                  left: 0,\r\n                  right: 0,\r\n                  bottom: 0,\r\n                  background: `linear-gradient(135deg, \r\n                    rgba(156, 39, 176, 0.1) 0%, \r\n                    rgba(245, 0, 87, 0.1) 100%)`,\r\n                  opacity: hoveredIndex === index ? 1 : 0,\r\n                  transition: 'opacity 0.3s ease',\r\n                  zIndex: 0,\r\n                }}\r\n              />\r\n\r\n              <Box sx={{ position: 'relative', zIndex: 1 }}>\r\n                <DecryptedText\r\n                  text={project.title}\r\n                  variant=\"h5\"\r\n                  className=\"glow-text\"\r\n                  sx={{ mb: 2 }}\r\n                  speed={60}\r\n                />\r\n\r\n                <Typography \r\n                  variant=\"body1\" \r\n                  sx={{ mb: 3, minHeight: '60px' }}\r\n                >\r\n                  {project.description}\r\n                </Typography>\r\n\r\n                <Box sx={{ mb: 3 }}>\r\n                  {project.tech.map((tech, idx) => (\r\n                    <Chip\r\n                      key={idx}\r\n                      label={tech}\r\n                      sx={{\r\n                        m: 0.5,\r\n                        background: 'linear-gradient(45deg, #9c27b0, #f50057)',\r\n                        color: 'white',\r\n                        '&:hover': {\r\n                          transform: 'scale(1.1)',\r\n                        },\r\n                        transition: 'transform 0.2s ease',\r\n                      }}\r\n                    />\r\n                  ))}\r\n                </Box>\r\n\r\n                <Box sx={{ \r\n                  display: 'flex', \r\n                  flexDirection: 'column', \r\n                  gap: 1 \r\n                }}>\r\n                  <Typography variant=\"subtitle2\" className=\"animated-underline\">\r\n                    Key Features:\r\n                  </Typography>\r\n                  {project.features.map((feature, idx) => (\r\n                    <Typography \r\n                      key={idx}\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        '&::before': {\r\n                          content: '\"\"',\r\n                          width: '8px',\r\n                          height: '8px',\r\n                          borderRadius: '50%',\r\n                          background: 'linear-gradient(45deg, #9c27b0, #f50057)',\r\n                          marginRight: '8px',\r\n                        },\r\n                      }}\r\n                    >\r\n                      {feature}\r\n                    </Typography>\r\n                  ))}\r\n                </Box>\r\n\r\n                <Box sx={{ \r\n                  display: 'flex', \r\n                  gap: 2, \r\n                  mt: 3,\r\n                  justifyContent: 'flex-end' \r\n                }}>\r\n                  <Link \r\n                    href={project.github} \r\n                    target=\"_blank\"\r\n                    sx={{\r\n                      color: 'inherit',\r\n                      '&:hover': {\r\n                        color: '#9c27b0',\r\n                      },\r\n                    }}\r\n                  >\r\n                    <GitHubIcon />\r\n                  </Link>\r\n                  <Link \r\n                    href=\"#\" \r\n                    target=\"_blank\"\r\n                    sx={{\r\n                      color: 'inherit',\r\n                      '&:hover': {\r\n                        color: '#f50057',\r\n                      },\r\n                    }}\r\n                  >\r\n                    <LaunchIcon />\r\n                  </Link>\r\n                </Box>\r\n              </Box>\r\n            </Paper>\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n      )}\r\n\r\n      {activeTab === 1 && (\r\n        <Box sx={{ mt: 2 }}>\r\n          <ProjectShowcase />\r\n        </Box>\r\n      )}\r\n\r\n      {activeTab === 2 && (\r\n        <Box sx={{ mt: 2 }}>\r\n          <InteractiveTimeline />\r\n        </Box>\r\n      )}\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default Projects; \r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAe;AAC9F,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,aAAa,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAE7C,MAAMuB,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CH,YAAY,CAACG,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,oJAAoJ;IACjKC,IAAI,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,iBAAiB,CAAC;IACrEC,KAAK,EAAE,gCAAgC;IACvCC,MAAM,EAAE,mDAAmD;IAC3DC,QAAQ,EAAE,CACR,qDAAqD,EACrD,6DAA6D,EAC7D,sEAAsE,EACtE,0CAA0C;EAE9C,CAAC,EACD;IACEL,KAAK,EAAE,8CAA8C;IACrDC,WAAW,EAAE,wIAAwI;IACrJC,IAAI,EAAE,CAAC,iBAAiB,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC;IAC/DC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,wCAAwC;IAChDC,QAAQ,EAAE,CACR,0DAA0D,EAC1D,oEAAoE,EACpE,wDAAwD,EACxD,2DAA2D;EAE/D,CAAC,EACD;IACEL,KAAK,EAAE,qCAAqC;IAC5CC,WAAW,EAAE,kHAAkH;IAC/HC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,CAAC;IACnEC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,mDAAmD;IAC3DC,QAAQ,EAAE,CACR,0EAA0E,EAC1E,2EAA2E,EAC3E,2EAA2E,EAC3E,iEAAiE;EAErE,CAAC,EACD;IACEL,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,8GAA8G;IAC3HC,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,wBAAwB,EAAE,OAAO,CAAC;IAChEC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,gEAAgE;IACxEC,QAAQ,EAAE,CACR,sCAAsC,EACtC,oCAAoC,EACpC,uCAAuC,EACvC,qCAAqC;EAEzC,CAAC,EACD;IACEL,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,4HAA4H;IACzIC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC;IAChDC,KAAK,EAAE,kBAAkB;IACzBC,MAAM,EAAE,iDAAiD;IACzDC,QAAQ,EAAE,CACR,uCAAuC,EACvC,+BAA+B,EAC/B,+BAA+B,EAC/B,6BAA6B;EAEjC,CAAC,EACD;IACEL,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,yHAAyH;IACtIC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,kBAAkB,CAAC;IAC3DC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,qDAAqD;IAC7DC,QAAQ,EAAE,CACR,4BAA4B,EAC5B,6BAA6B,EAC7B,8BAA8B,EAC9B,mCAAmC;EAEvC,CAAC,EACD;IACEL,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,0EAA0E;IACvFC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;IAChDC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,oCAAoC;IAC5CC,QAAQ,EAAE,CACR,6BAA6B,EAC7B,yBAAyB,EACzB,qBAAqB,EACrB,qBAAqB;EAEzB,CAAC,EACD;IACEL,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,CAAC;IACrDC,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE,oCAAoC;IAC5CC,QAAQ,EAAE,CACR,wBAAwB,EACxB,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB;EAEzB,CAAC,CACF;EAED,oBACEhB,OAAA,CAACf,SAAS;IAACgC,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACrCpB,OAAA,CAACF,aAAa;MACZuB,IAAI,EAAC,mBAAmB;MACxBC,OAAO,EAAC,IAAI;MACZC,SAAS,EAAC,0BAA0B;MACpCL,EAAE,EAAE;QAAEM,EAAE,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MACnCC,KAAK,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGF9B,OAAA,CAACX,GAAG;MAAC6B,EAAE,EAAE;QAAEa,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAER,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC1DpB,OAAA,CAACR,IAAI;QACHyC,KAAK,EAAE5B,SAAU;QACjB6B,QAAQ,EAAE3B,eAAgB;QAC1B4B,QAAQ;QACRjB,EAAE,EAAE;UACF,gBAAgB,EAAE;YAChBkB,KAAK,EAAE,gBAAgB;YACvB,gBAAgB,EAAE;cAChBA,KAAK,EAAE;YACT;UACF,CAAC;UACD,sBAAsB,EAAE;YACtBC,UAAU,EAAE,0CAA0C;YACtDC,MAAM,EAAE;UACV;QACF,CAAE;QAAAlB,QAAA,gBAEFpB,OAAA,CAACP,GAAG;UAAC8C,KAAK,EAAC;QAAc;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B9B,OAAA,CAACP,GAAG;UAAC8C,KAAK,EAAC;QAAmB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjC9B,OAAA,CAACP,GAAG;UAAC8C,KAAK,EAAC;QAAkB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLzB,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACb,IAAI;MAACqD,SAAS;MAACC,OAAO,EAAE,CAAE;MAAArB,QAAA,EACxBV,QAAQ,CAACgC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC7B5C,OAAA,CAACb,IAAI;QAAC0D,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eACvBpB,OAAA,CAACZ,KAAK;UACJmC,SAAS,EAAC,cAAc;UACxBL,EAAE,EAAE;YACF8B,CAAC,EAAE,CAAC;YACJV,MAAM,EAAE,MAAM;YACdW,QAAQ,EAAE,UAAU;YACpBC,SAAS,EAAE/C,YAAY,KAAKyC,KAAK,GAAG,kBAAkB,GAAG,MAAM;YAC/DO,UAAU,EAAE,mBAAmB;YAC/B,SAAS,EAAE;cACTD,SAAS,EAAE;YACb,CAAC;YACDE,WAAW,EAAE;UACf,CAAE;UACFC,YAAY,EAAEA,CAAA,KAAMjD,eAAe,CAACwC,KAAK,CAAE;UAC3CU,YAAY,EAAEA,CAAA,KAAMlD,eAAe,CAAC,IAAI,CAAE;UAAAgB,QAAA,gBAE1CpB,OAAA,CAACX,GAAG;YACF6B,EAAE,EAAE;cACF+B,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTrB,UAAU,EAAE;AAC9B;AACA,gDAAgD;cAC9BsB,OAAO,EAAExD,YAAY,KAAKyC,KAAK,GAAG,CAAC,GAAG,CAAC;cACvCO,UAAU,EAAE,mBAAmB;cAC/BS,MAAM,EAAE;YACV;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF9B,OAAA,CAACX,GAAG;YAAC6B,EAAE,EAAE;cAAE+B,QAAQ,EAAE,UAAU;cAAEW,MAAM,EAAE;YAAE,CAAE;YAAAxC,QAAA,gBAC3CpB,OAAA,CAACF,aAAa;cACZuB,IAAI,EAAEsB,OAAO,CAAChC,KAAM;cACpBW,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,WAAW;cACrBL,EAAE,EAAE;gBAAEM,EAAE,EAAE;cAAE,CAAE;cACdE,KAAK,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEF9B,OAAA,CAACd,UAAU;cACToC,OAAO,EAAC,OAAO;cACfJ,EAAE,EAAE;gBAAEM,EAAE,EAAE,CAAC;gBAAEqC,SAAS,EAAE;cAAO,CAAE;cAAAzC,QAAA,EAEhCuB,OAAO,CAAC/B;YAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEb9B,OAAA,CAACX,GAAG;cAAC6B,EAAE,EAAE;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAChBuB,OAAO,CAAC9B,IAAI,CAAC6B,GAAG,CAAC,CAAC7B,IAAI,EAAEiD,GAAG,kBAC1B9D,OAAA,CAACV,IAAI;gBAEHiD,KAAK,EAAE1B,IAAK;gBACZK,EAAE,EAAE;kBACF6C,CAAC,EAAE,GAAG;kBACN1B,UAAU,EAAE,0CAA0C;kBACtDD,KAAK,EAAE,OAAO;kBACd,SAAS,EAAE;oBACTc,SAAS,EAAE;kBACb,CAAC;kBACDC,UAAU,EAAE;gBACd;cAAE,GAVGW,GAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWT,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9B,OAAA,CAACX,GAAG;cAAC6B,EAAE,EAAE;gBACP8C,OAAO,EAAE,MAAM;gBACfC,aAAa,EAAE,QAAQ;gBACvBC,GAAG,EAAE;cACP,CAAE;cAAA9C,QAAA,gBACApB,OAAA,CAACd,UAAU;gBAACoC,OAAO,EAAC,WAAW;gBAACC,SAAS,EAAC,oBAAoB;gBAAAH,QAAA,EAAC;cAE/D;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZa,OAAO,CAAC3B,QAAQ,CAAC0B,GAAG,CAAC,CAACyB,OAAO,EAAEL,GAAG,kBACjC9D,OAAA,CAACd,UAAU;gBAEToC,OAAO,EAAC,OAAO;gBACfJ,EAAE,EAAE;kBACF8C,OAAO,EAAE,MAAM;kBACfI,UAAU,EAAE,QAAQ;kBACpB,WAAW,EAAE;oBACXC,OAAO,EAAE,IAAI;oBACbC,KAAK,EAAE,KAAK;oBACZhC,MAAM,EAAE,KAAK;oBACbiC,YAAY,EAAE,KAAK;oBACnBlC,UAAU,EAAE,0CAA0C;oBACtDmC,WAAW,EAAE;kBACf;gBACF,CAAE;gBAAApD,QAAA,EAED+C;cAAO,GAfHL,GAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBE,CACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9B,OAAA,CAACX,GAAG;cAAC6B,EAAE,EAAE;gBACP8C,OAAO,EAAE,MAAM;gBACfE,GAAG,EAAE,CAAC;gBACN/C,EAAE,EAAE,CAAC;gBACLsD,cAAc,EAAE;cAClB,CAAE;cAAArD,QAAA,gBACApB,OAAA,CAACT,IAAI;gBACHmF,IAAI,EAAE/B,OAAO,CAAC5B,MAAO;gBACrB4D,MAAM,EAAC,QAAQ;gBACfzD,EAAE,EAAE;kBACFkB,KAAK,EAAE,SAAS;kBAChB,SAAS,EAAE;oBACTA,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAhB,QAAA,eAEFpB,OAAA,CAACN,UAAU;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACP9B,OAAA,CAACT,IAAI;gBACHmF,IAAI,EAAC,GAAG;gBACRC,MAAM,EAAC,QAAQ;gBACfzD,EAAE,EAAE;kBACFkB,KAAK,EAAE,SAAS;kBAChB,SAAS,EAAE;oBACTA,KAAK,EAAE;kBACT;gBACF,CAAE;gBAAAhB,QAAA,eAEFpB,OAAA,CAACL,UAAU;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC,GAjIqBc,KAAK;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkI5B,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,EAEAzB,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACX,GAAG;MAAC6B,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACjBpB,OAAA,CAACJ,eAAe;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACN,EAEAzB,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACX,GAAG;MAAC6B,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACjBpB,OAAA,CAACH,mBAAmB;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB;AAAC5B,EAAA,CA9SQD,QAAQ;AAAA2E,EAAA,GAAR3E,QAAQ;AAgTjB,eAAeA,QAAQ;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}