import React, { useState } from 'react';
import { Paper, Box } from '@mui/material';

const GlowCard = ({ 
  children, 
  glowColor = '#007AFF', 
  intensity = 0.5,
  sx = {},
  ...props 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Paper
      {...props}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        transform: isHovered ? 'translateY(-8px)' : 'translateY(0)',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, ${glowColor}20 0%, transparent 50%, ${glowColor}10 100%)`,
          opacity: isHovered ? intensity : intensity * 0.3,
          transition: 'opacity 0.3s ease',
          pointerEvents: 'none',
          zIndex: 0,
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: -2,
          left: -2,
          right: -2,
          bottom: -2,
          background: `linear-gradient(45deg, ${glowColor}60, transparent, ${glowColor}60)`,
          borderRadius: 'inherit',
          opacity: isHovered ? 0.6 : 0,
          transition: 'opacity 0.3s ease',
          pointerEvents: 'none',
          zIndex: -1,
          filter: 'blur(8px)',
        },
        boxShadow: isHovered 
          ? `0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px ${glowColor}40`
          : '0 8px 32px rgba(0, 0, 0, 0.2)',
        ...sx
      }}
    >
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        {children}
      </Box>
    </Paper>
  );
};

export default GlowCard;
