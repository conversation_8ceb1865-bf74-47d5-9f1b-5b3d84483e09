import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';

const GridDistortion = ({ children, gridSize = 40, distortionStrength = 0.2, color = '#9c27b0', opacity = 0.1 }) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationId;
    let time = 0;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const drawGrid = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const cols = Math.ceil(canvas.width / gridSize);
      const rows = Math.ceil(canvas.height / gridSize);

      ctx.strokeStyle = `${color}20`;
      ctx.lineWidth = 0.5;

      // Draw vertical lines
      for (let i = 0; i <= cols; i++) {
        ctx.beginPath();
        for (let j = 0; j <= rows; j++) {
          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;
          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;
          
          if (j === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.stroke();
      }

      // Draw horizontal lines
      for (let j = 0; j <= rows; j++) {
        ctx.beginPath();
        for (let i = 0; i <= cols; i++) {
          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;
          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;
          
          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.stroke();
      }

      // Add intersection points with glow
      ctx.fillStyle = `${color}80`;
      ctx.shadowColor = color;
      ctx.shadowBlur = 5;
      
      for (let i = 0; i <= cols; i += 2) {
        for (let j = 0; j <= rows; j += 2) {
          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;
          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;
          
          const pulseSize = 2 + Math.sin(time * 0.02 + i + j) * 1;
          ctx.beginPath();
          ctx.arc(x, y, pulseSize, 0, Math.PI * 2);
          ctx.fill();
        }
      }
      
      ctx.shadowBlur = 0;
    };

    const animate = () => {
      time += 1;
      drawGrid();
      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [gridSize, distortionStrength, color]);

  return (
    <Box sx={{ position: 'relative', overflow: 'hidden' }}>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 0,
          pointerEvents: 'none',
          opacity: opacity,
        }}
      />
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        {children}
      </Box>
    </Box>
  );
};

export default GridDistortion;
