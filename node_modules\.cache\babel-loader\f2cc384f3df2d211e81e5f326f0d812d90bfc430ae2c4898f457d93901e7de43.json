{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { createTheme, ThemeProvider } from '@mui/material/styles';\nimport { Box, Container } from '@mui/material';\nimport Header from './components/Header';\nimport About from './components/About';\nimport Education from './components/Education';\nimport Experience from './components/Experience';\nimport Projects from './components/Projects';\nimport Skills from './components/Skills';\nimport Certifications from './components/Certifications';\nimport Publications from './components/Publications';\nimport Contact from './components/Contact';\n// Removed CustomCursor for better performance\nimport FloatingActionMenu from './components/FloatingActionMenu';\nimport ThemeToggle from './components/ThemeToggle';\nimport { HyperspeedBackground, SubtleParticles, EnhancedSection, AuroraBackground, GridDistortion } from './components/effects';\nimport './styles/transitions.css';\nimport './styles/animations.css';\n// Apple-style smooth transitions - no heavy animations\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst createScope = options => {\n  const animations = [];\n  const methods = {};\n  const add = (callback, fn) => {\n    if (typeof callback === 'string' && fn) {\n      methods[callback] = fn;\n    } else if (typeof callback === 'function') {\n      callback({\n        animate,\n        timeline: animate.timeline,\n        add\n      });\n    }\n    return scope;\n  };\n  const revert = () => {\n    animations.forEach(anim => anim.pause());\n    animations.length = 0;\n  };\n  const scope = {\n    add,\n    revert,\n    methods,\n    animations\n  };\n  return scope;\n};\nconst theme = createTheme({\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: '#007AFF',\n      // Apple Blue\n      light: '#5AC8FA',\n      dark: '#0051D5'\n    },\n    secondary: {\n      main: '#FF9500',\n      // Apple Orange\n      light: '#FFCC02',\n      dark: '#FF6B00'\n    },\n    background: {\n      default: '#000000',\n      // Pure black like Apple\n      paper: 'rgba(28, 28, 30, 0.95)' // Apple dark gray with transparency\n    },\n    text: {\n      primary: '#FFFFFF',\n      // Pure white\n      secondary: 'rgba(255, 255, 255, 0.6)' // Apple secondary text\n    },\n    success: {\n      main: '#30D158' // Apple Green\n    },\n    warning: {\n      main: '#FF9500' // Apple Orange\n    },\n    error: {\n      main: '#FF453A' // Apple Red\n    },\n    info: {\n      main: '#64D2FF' // Apple Light Blue\n    }\n  },\n  typography: {\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"SF Pro Display\", \"SF Pro Text\", \"Helvetica Neue\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: 'clamp(2.5rem, 6vw, 4rem)',\n      fontWeight: 700,\n      lineHeight: 1.1,\n      letterSpacing: '-0.025em'\n    },\n    h2: {\n      fontSize: 'clamp(2rem, 5vw, 3rem)',\n      fontWeight: 600,\n      lineHeight: 1.2,\n      letterSpacing: '-0.02em'\n    },\n    h3: {\n      fontSize: 'clamp(1.75rem, 4vw, 2.5rem)',\n      fontWeight: 600,\n      lineHeight: 1.3,\n      letterSpacing: '-0.015em'\n    },\n    h4: {\n      fontSize: 'clamp(1.5rem, 3vw, 2rem)',\n      fontWeight: 600,\n      lineHeight: 1.4,\n      letterSpacing: '-0.01em'\n    },\n    h5: {\n      fontSize: 'clamp(1.25rem, 2.5vw, 1.5rem)',\n      fontWeight: 500,\n      lineHeight: 1.5\n    },\n    h6: {\n      fontSize: 'clamp(1rem, 2vw, 1.25rem)',\n      fontWeight: 500,\n      lineHeight: 1.6\n    },\n    body1: {\n      fontSize: 'clamp(1rem, 1.5vw, 1.125rem)',\n      lineHeight: 1.7,\n      fontWeight: 400\n    },\n    body2: {\n      fontSize: 'clamp(0.875rem, 1.25vw, 1rem)',\n      lineHeight: 1.6,\n      fontWeight: 400\n    }\n  },\n  components: {\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: 'rgba(28, 28, 30, 0.8)',\n          // Apple dark with transparency\n          backdropFilter: 'blur(20px)',\n          // Apple-style blur\n          borderBottom: '0.5px solid rgba(255, 255, 255, 0.1)',\n          boxShadow: 'none' // Apple style - minimal shadows\n        }\n      }\n    },\n    MuiContainer: {\n      styleOverrides: {\n        root: {\n          scrollMarginTop: '80px',\n          padding: '0 clamp(1rem, 4vw, 2rem)'\n        }\n      }\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none',\n          backgroundColor: 'rgba(28, 28, 30, 0.8)',\n          // Apple dark card\n          backdropFilter: 'blur(20px)',\n          // Apple-style blur\n          border: '0.5px solid rgba(255, 255, 255, 0.1)',\n          // Subtle border\n          borderRadius: '12px',\n          // Apple-style radius\n          '&:hover': {\n            backgroundColor: 'rgba(28, 28, 30, 0.9)',\n            transform: 'translateY(-1px)' // Subtle lift\n          },\n          transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)' // Apple easing\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: '8px',\n          // Apple-style radius\n          textTransform: 'none',\n          fontWeight: 500,\n          padding: '12px 24px',\n          fontSize: '0.875rem',\n          fontFamily: '-apple-system, BlinkMacSystemFont, \"SF Pro Text\", sans-serif',\n          '&:hover': {\n            transform: 'scale(1.02)' // Apple-style scale\n          },\n          transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)' // Apple easing\n        },\n        contained: {\n          background: '#007AFF',\n          // Apple blue\n          color: '#FFFFFF',\n          boxShadow: 'none',\n          '&:hover': {\n            background: '#0051D5',\n            boxShadow: 'none'\n          }\n        },\n        outlined: {\n          borderColor: 'rgba(0, 122, 255, 0.5)',\n          color: '#007AFF',\n          '&:hover': {\n            borderColor: '#007AFF',\n            backgroundColor: 'rgba(0, 122, 255, 0.1)'\n          }\n        }\n      }\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          borderRadius: '8px',\n          fontWeight: 500,\n          fontSize: '0.75rem'\n        }\n      }\n    }\n  },\n  breakpoints: {\n    values: {\n      xs: 0,\n      sm: 600,\n      md: 960,\n      lg: 1280,\n      xl: 1920\n    }\n  }\n});\nconst SectionContainer = ({\n  id,\n  children\n}) => {\n  _s();\n  useEffect(() => {\n    // Apple-style smooth slide-in animation\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('slide-in');\n        }\n      });\n    }, {\n      threshold: 0.1,\n      rootMargin: '0px 0px -100px 0px'\n    });\n    const content = document.querySelector(`#${id}-content`);\n    if (content) {\n      observer.observe(content);\n    }\n    return () => {\n      if (content) {\n        observer.unobserve(content);\n      }\n    };\n  }, [id]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    id: id,\n    component: \"section\",\n    sx: {\n      minHeight: {\n        xs: 'auto',\n        md: '100vh'\n      },\n      display: 'flex',\n      alignItems: 'center',\n      py: {\n        xs: 8,\n        sm: 12,\n        md: 16\n      },\n      // More Apple-like spacing\n      px: {\n        xs: 3,\n        sm: 4,\n        md: 6\n      },\n      scrollMarginTop: {\n        xs: '56px',\n        sm: '64px'\n      },\n      position: 'relative'\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      id: `${id}-content`,\n      maxWidth: \"lg\",\n      className: \"slide-container\",\n      sx: {\n        width: '100%',\n        mx: 'auto',\n        px: {\n          xs: 3,\n          sm: 4,\n          md: 6\n        },\n        opacity: 0,\n        transform: 'translateY(40px)',\n        transition: 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n        // Apple easing\n        '&.slide-in': {\n          opacity: 1,\n          transform: 'translateY(0)'\n        }\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n_s(SectionContainer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = SectionContainer;\nfunction AppContent() {\n  return /*#__PURE__*/_jsxDEV(SubtleParticles, {\n    particleCount: 30,\n    color: \"#007AFF\",\n    opacity: 0.15,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: '100vh',\n        backgroundColor: '#000000',\n        // Pure black like Apple\n        position: 'relative',\n        background: 'linear-gradient(180deg, #000000 0%, #1a1a1a 100%)' // Apple-style gradient\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: `\n              radial-gradient(circle at 20% 20%, rgba(0, 122, 255, 0.03) 0%, transparent 50%),\n              radial-gradient(circle at 80% 80%, rgba(255, 149, 0, 0.02) 0%, transparent 50%)\n            `,\n          pointerEvents: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          top: 20,\n          right: 20,\n          zIndex: 1000\n        },\n        children: /*#__PURE__*/_jsxDEV(ThemeToggle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'relative',\n          height: '64px'\n        },\n        children: /*#__PURE__*/_jsxDEV(HyperspeedBackground, {\n          intensity: 0.2,\n          color: \"#007AFF\",\n          opacity: 0.05,\n          children: /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: \"main\",\n        sx: {\n          position: 'relative',\n          scrollBehavior: 'smooth',\n          pt: '64px',\n          // Add padding top to account for fixed header\n          '& *::-webkit-scrollbar': {\n            width: '6px'\n          },\n          '& *::-webkit-scrollbar-track': {\n            background: '#1e293b'\n          },\n          '& *::-webkit-scrollbar-thumb': {\n            background: '#6366f1',\n            borderRadius: '3px',\n            '&:hover': {\n              background: '#4f46e5'\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"about\",\n          children: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"education\",\n          children: /*#__PURE__*/_jsxDEV(Education, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"experience\",\n          children: /*#__PURE__*/_jsxDEV(Experience, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"projects\",\n          children: /*#__PURE__*/_jsxDEV(Projects, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"skills\",\n          children: /*#__PURE__*/_jsxDEV(Skills, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"certifications\",\n          children: /*#__PURE__*/_jsxDEV(Certifications, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"publications\",\n          children: /*#__PURE__*/_jsxDEV(Publications, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(SectionContainer, {\n          id: \"contact\",\n          children: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(FloatingActionMenu, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n}\n_c2 = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 375,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"SectionContainer\");\n$RefreshReg$(_c2, \"AppContent\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "createTheme", "ThemeProvider", "Box", "Container", "Header", "About", "Education", "Experience", "Projects", "Skills", "Certifications", "Publications", "Contact", "FloatingActionMenu", "ThemeToggle", "HyperspeedBackground", "SubtleParticles", "EnhancedSection", "AuroraBackground", "GridDistortion", "jsxDEV", "_jsxDEV", "createScope", "options", "animations", "methods", "add", "callback", "fn", "animate", "timeline", "scope", "revert", "for<PERSON>ach", "anim", "pause", "length", "theme", "palette", "mode", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "success", "warning", "error", "info", "typography", "fontFamily", "h1", "fontSize", "fontWeight", "lineHeight", "letterSpacing", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "components", "MuiAppBar", "styleOverrides", "root", "backgroundColor", "<PERSON><PERSON>ilter", "borderBottom", "boxShadow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollMarginTop", "padding", "MuiPaper", "backgroundImage", "border", "borderRadius", "transform", "transition", "MuiB<PERSON>on", "textTransform", "contained", "color", "outlined", "borderColor", "MuiChip", "breakpoints", "values", "xs", "sm", "md", "lg", "xl", "SectionContainer", "id", "children", "_s", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "target", "classList", "threshold", "rootMargin", "content", "document", "querySelector", "observe", "unobserve", "component", "sx", "minHeight", "display", "alignItems", "py", "px", "position", "max<PERSON><PERSON><PERSON>", "className", "width", "mx", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "A<PERSON><PERSON><PERSON>nt", "particleCount", "top", "left", "right", "bottom", "pointerEvents", "zIndex", "height", "intensity", "scroll<PERSON>eh<PERSON>or", "pt", "_c2", "App", "_c3", "$RefreshReg$"], "sources": ["D:/Resume-main/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\r\nimport { createTheme, ThemeProvider } from '@mui/material/styles';\r\nimport { Box, Container } from '@mui/material';\r\nimport Header from './components/Header';\r\nimport About from './components/About';\r\nimport Education from './components/Education';\r\nimport Experience from './components/Experience';\r\nimport Projects from './components/Projects';\r\nimport Skills from './components/Skills';\r\nimport Certifications from './components/Certifications';\r\nimport Publications from './components/Publications';\r\nimport Contact from './components/Contact';\r\n// Removed CustomCursor for better performance\r\nimport FloatingActionMenu from './components/FloatingActionMenu';\r\nimport ThemeToggle from './components/ThemeToggle';\r\nimport { HyperspeedBackground, SubtleParticles, EnhancedSection, AuroraBackground, GridDistortion } from './components/effects';\r\nimport './styles/transitions.css';\r\nimport './styles/animations.css';\r\n// Apple-style smooth transitions - no heavy animations\r\n\r\nconst createScope = (options) => {\r\n  const animations = [];\r\n  const methods = {};\r\n\r\n  const add = (callback, fn) => {\r\n    if (typeof callback === 'string' && fn) {\r\n      methods[callback] = fn;\r\n    } else if (typeof callback === 'function') {\r\n      callback({ animate, timeline: animate.timeline, add });\r\n    }\r\n    return scope;\r\n  };\r\n\r\n  const revert = () => {\r\n    animations.forEach(anim => anim.pause());\r\n    animations.length = 0;\r\n  };\r\n\r\n  const scope = {\r\n    add,\r\n    revert,\r\n    methods,\r\n    animations\r\n  };\r\n\r\n  return scope;\r\n};\r\n\r\nconst theme = createTheme({\r\n  palette: {\r\n    mode: 'dark',\r\n    primary: {\r\n      main: '#007AFF', // Apple Blue\r\n      light: '#5AC8FA',\r\n      dark: '#0051D5',\r\n    },\r\n    secondary: {\r\n      main: '#FF9500', // Apple Orange\r\n      light: '#FFCC02',\r\n      dark: '#FF6B00',\r\n    },\r\n    background: {\r\n      default: '#000000', // Pure black like Apple\r\n      paper: 'rgba(28, 28, 30, 0.95)', // Apple dark gray with transparency\r\n    },\r\n    text: {\r\n      primary: '#FFFFFF', // Pure white\r\n      secondary: 'rgba(255, 255, 255, 0.6)', // Apple secondary text\r\n    },\r\n    success: {\r\n      main: '#30D158', // Apple Green\r\n    },\r\n    warning: {\r\n      main: '#FF9500', // Apple Orange\r\n    },\r\n    error: {\r\n      main: '#FF453A', // Apple Red\r\n    },\r\n    info: {\r\n      main: '#64D2FF', // Apple Light Blue\r\n    },\r\n  },\r\n  typography: {\r\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"SF Pro Display\", \"SF Pro Text\", \"Helvetica Neue\", \"Arial\", sans-serif',\r\n    h1: {\r\n      fontSize: 'clamp(2.5rem, 6vw, 4rem)',\r\n      fontWeight: 700,\r\n      lineHeight: 1.1,\r\n      letterSpacing: '-0.025em',\r\n    },\r\n    h2: {\r\n      fontSize: 'clamp(2rem, 5vw, 3rem)',\r\n      fontWeight: 600,\r\n      lineHeight: 1.2,\r\n      letterSpacing: '-0.02em',\r\n    },\r\n    h3: {\r\n      fontSize: 'clamp(1.75rem, 4vw, 2.5rem)',\r\n      fontWeight: 600,\r\n      lineHeight: 1.3,\r\n      letterSpacing: '-0.015em',\r\n    },\r\n    h4: {\r\n      fontSize: 'clamp(1.5rem, 3vw, 2rem)',\r\n      fontWeight: 600,\r\n      lineHeight: 1.4,\r\n      letterSpacing: '-0.01em',\r\n    },\r\n    h5: {\r\n      fontSize: 'clamp(1.25rem, 2.5vw, 1.5rem)',\r\n      fontWeight: 500,\r\n      lineHeight: 1.5,\r\n    },\r\n    h6: {\r\n      fontSize: 'clamp(1rem, 2vw, 1.25rem)',\r\n      fontWeight: 500,\r\n      lineHeight: 1.6,\r\n    },\r\n    body1: {\r\n      fontSize: 'clamp(1rem, 1.5vw, 1.125rem)',\r\n      lineHeight: 1.7,\r\n      fontWeight: 400,\r\n    },\r\n    body2: {\r\n      fontSize: 'clamp(0.875rem, 1.25vw, 1rem)',\r\n      lineHeight: 1.6,\r\n      fontWeight: 400,\r\n    },\r\n  },\r\n  components: {\r\n    MuiAppBar: {\r\n      styleOverrides: {\r\n        root: {\r\n          backgroundColor: 'rgba(28, 28, 30, 0.8)', // Apple dark with transparency\r\n          backdropFilter: 'blur(20px)', // Apple-style blur\r\n          borderBottom: '0.5px solid rgba(255, 255, 255, 0.1)',\r\n          boxShadow: 'none', // Apple style - minimal shadows\r\n        },\r\n      },\r\n    },\r\n    MuiContainer: {\r\n      styleOverrides: {\r\n        root: {\r\n          scrollMarginTop: '80px',\r\n          padding: '0 clamp(1rem, 4vw, 2rem)',\r\n        },\r\n      },\r\n    },\r\n    MuiPaper: {\r\n      styleOverrides: {\r\n        root: {\r\n          backgroundImage: 'none',\r\n          backgroundColor: 'rgba(28, 28, 30, 0.8)', // Apple dark card\r\n          backdropFilter: 'blur(20px)', // Apple-style blur\r\n          border: '0.5px solid rgba(255, 255, 255, 0.1)', // Subtle border\r\n          borderRadius: '12px', // Apple-style radius\r\n          '&:hover': {\r\n            backgroundColor: 'rgba(28, 28, 30, 0.9)',\r\n            transform: 'translateY(-1px)', // Subtle lift\r\n          },\r\n          transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Apple easing\r\n        },\r\n      },\r\n    },\r\n    MuiButton: {\r\n      styleOverrides: {\r\n        root: {\r\n          borderRadius: '8px', // Apple-style radius\r\n          textTransform: 'none',\r\n          fontWeight: 500,\r\n          padding: '12px 24px',\r\n          fontSize: '0.875rem',\r\n          fontFamily: '-apple-system, BlinkMacSystemFont, \"SF Pro Text\", sans-serif',\r\n          '&:hover': {\r\n            transform: 'scale(1.02)', // Apple-style scale\r\n          },\r\n          transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Apple easing\r\n        },\r\n        contained: {\r\n          background: '#007AFF', // Apple blue\r\n          color: '#FFFFFF',\r\n          boxShadow: 'none',\r\n          '&:hover': {\r\n            background: '#0051D5',\r\n            boxShadow: 'none',\r\n          },\r\n        },\r\n        outlined: {\r\n          borderColor: 'rgba(0, 122, 255, 0.5)',\r\n          color: '#007AFF',\r\n          '&:hover': {\r\n            borderColor: '#007AFF',\r\n            backgroundColor: 'rgba(0, 122, 255, 0.1)',\r\n          },\r\n        },\r\n      },\r\n    },\r\n    MuiChip: {\r\n      styleOverrides: {\r\n        root: {\r\n          borderRadius: '8px',\r\n          fontWeight: 500,\r\n          fontSize: '0.75rem',\r\n        },\r\n      },\r\n    },\r\n  },\r\n  breakpoints: {\r\n    values: {\r\n      xs: 0,\r\n      sm: 600,\r\n      md: 960,\r\n      lg: 1280,\r\n      xl: 1920,\r\n    },\r\n  },\r\n});\r\n\r\nconst SectionContainer = ({ id, children }) => {\r\n  useEffect(() => {\r\n    // Apple-style smooth slide-in animation\r\n    const observer = new IntersectionObserver((entries) => {\r\n      entries.forEach(entry => {\r\n        if (entry.isIntersecting) {\r\n          entry.target.classList.add('slide-in');\r\n        }\r\n      });\r\n    }, {\r\n      threshold: 0.1,\r\n      rootMargin: '0px 0px -100px 0px'\r\n    });\r\n\r\n    const content = document.querySelector(`#${id}-content`);\r\n    if (content) {\r\n      observer.observe(content);\r\n    }\r\n\r\n    return () => {\r\n      if (content) {\r\n        observer.unobserve(content);\r\n      }\r\n    };\r\n  }, [id]);\r\n\r\n  return (\r\n    <Box\r\n      id={id}\r\n      component=\"section\"\r\n      sx={{\r\n        minHeight: { xs: 'auto', md: '100vh' },\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        py: { xs: 8, sm: 12, md: 16 }, // More Apple-like spacing\r\n        px: { xs: 3, sm: 4, md: 6 },\r\n        scrollMarginTop: { xs: '56px', sm: '64px' },\r\n        position: 'relative',\r\n      }}\r\n    >\r\n      <Container\r\n        id={`${id}-content`}\r\n        maxWidth=\"lg\"\r\n        className=\"slide-container\"\r\n        sx={{\r\n          width: '100%',\r\n          mx: 'auto',\r\n          px: { xs: 3, sm: 4, md: 6 },\r\n          opacity: 0,\r\n          transform: 'translateY(40px)',\r\n          transition: 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Apple easing\r\n          '&.slide-in': {\r\n            opacity: 1,\r\n            transform: 'translateY(0)',\r\n          }\r\n        }}\r\n      >\r\n        {children}\r\n      </Container>\r\n    </Box>\r\n  );\r\n};\r\n\r\nfunction AppContent() {\r\n  return (\r\n    <SubtleParticles particleCount={30} color=\"#007AFF\" opacity={0.15}>\r\n      <Box sx={{\r\n        minHeight: '100vh',\r\n        backgroundColor: '#000000', // Pure black like Apple\r\n        position: 'relative',\r\n        background: 'linear-gradient(180deg, #000000 0%, #1a1a1a 100%)', // Apple-style gradient\r\n      }}>\r\n        {/* Apple-style subtle background pattern */}\r\n        <Box\r\n          sx={{\r\n            position: 'fixed',\r\n            top: 0,\r\n            left: 0,\r\n            right: 0,\r\n            bottom: 0,\r\n            background: `\r\n              radial-gradient(circle at 20% 20%, rgba(0, 122, 255, 0.03) 0%, transparent 50%),\r\n              radial-gradient(circle at 80% 80%, rgba(255, 149, 0, 0.02) 0%, transparent 50%)\r\n            `,\r\n            pointerEvents: 'none',\r\n          }}\r\n        />\r\n\r\n      {/* Removed heavy custom cursor for better performance */}\r\n\r\n      {/* Theme Toggle */}\r\n      <Box sx={{ position: 'fixed', top: 20, right: 20, zIndex: 1000 }}>\r\n        <ThemeToggle />\r\n      </Box>\r\n\r\n      <Box sx={{ position: 'relative', height: '64px' }}>\r\n        <HyperspeedBackground intensity={0.2} color=\"#007AFF\" opacity={0.05}>\r\n          <Header />\r\n        </HyperspeedBackground>\r\n      </Box>\r\n      <Box\r\n        component=\"main\"\r\n        sx={{\r\n          position: 'relative',\r\n          scrollBehavior: 'smooth',\r\n          pt: '64px', // Add padding top to account for fixed header\r\n          '& *::-webkit-scrollbar': {\r\n            width: '6px',\r\n          },\r\n          '& *::-webkit-scrollbar-track': {\r\n            background: '#1e293b',\r\n          },\r\n          '& *::-webkit-scrollbar-thumb': {\r\n            background: '#6366f1',\r\n            borderRadius: '3px',\r\n            '&:hover': {\r\n              background: '#4f46e5',\r\n            },\r\n          },\r\n        }}\r\n      >\r\n        <SectionContainer id=\"about\">\r\n          <About />\r\n        </SectionContainer>\r\n        <SectionContainer id=\"education\">\r\n          <Education />\r\n        </SectionContainer>\r\n        <SectionContainer id=\"experience\">\r\n          <Experience />\r\n        </SectionContainer>\r\n        <SectionContainer id=\"projects\">\r\n          <Projects />\r\n        </SectionContainer>\r\n        <SectionContainer id=\"skills\">\r\n          <Skills />\r\n        </SectionContainer>\r\n        <SectionContainer id=\"certifications\">\r\n          <Certifications />\r\n        </SectionContainer>\r\n        <SectionContainer id=\"publications\">\r\n          <Publications />\r\n        </SectionContainer>\r\n        <SectionContainer id=\"contact\">\r\n          <Contact />\r\n        </SectionContainer>\r\n      </Box>\r\n\r\n        {/* Floating Action Menu */}\r\n        <FloatingActionMenu />\r\n      </Box>\r\n    </SubtleParticles>\r\n  );\r\n}\r\n\r\nfunction App() {\r\n  return (\r\n    <ThemeProvider theme={theme}>\r\n      <AppContent />\r\n    </ThemeProvider>\r\n  );\r\n}\r\n\r\nexport default App; \r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,aAAa,QAAQ,sBAAsB;AACjE,SAASC,GAAG,EAAEC,SAAS,QAAQ,eAAe;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C;AACA,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,sBAAsB;AAC/H,OAAO,0BAA0B;AACjC,OAAO,yBAAyB;AAChC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,WAAW,GAAIC,OAAO,IAAK;EAC/B,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;EAElB,MAAMC,GAAG,GAAGA,CAACC,QAAQ,EAAEC,EAAE,KAAK;IAC5B,IAAI,OAAOD,QAAQ,KAAK,QAAQ,IAAIC,EAAE,EAAE;MACtCH,OAAO,CAACE,QAAQ,CAAC,GAAGC,EAAE;IACxB,CAAC,MAAM,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;MACzCA,QAAQ,CAAC;QAAEE,OAAO;QAAEC,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAAEJ;MAAI,CAAC,CAAC;IACxD;IACA,OAAOK,KAAK;EACd,CAAC;EAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBR,UAAU,CAACS,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC;IACxCX,UAAU,CAACY,MAAM,GAAG,CAAC;EACvB,CAAC;EAED,MAAML,KAAK,GAAG;IACZL,GAAG;IACHM,MAAM;IACNP,OAAO;IACPD;EACF,CAAC;EAED,OAAOO,KAAK;AACd,CAAC;AAED,MAAMM,KAAK,GAAGrC,WAAW,CAAC;EACxBsC,OAAO,EAAE;IACPC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAAE;MACpBC,KAAK,EAAE,wBAAwB,CAAE;IACnC,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,SAAS;MAAE;MACpBI,SAAS,EAAE,0BAA0B,CAAE;IACzC,CAAC;IACDK,OAAO,EAAE;MACPR,IAAI,EAAE,SAAS,CAAE;IACnB,CAAC;IACDS,OAAO,EAAE;MACPT,IAAI,EAAE,SAAS,CAAE;IACnB,CAAC;IACDU,KAAK,EAAE;MACLV,IAAI,EAAE,SAAS,CAAE;IACnB,CAAC;IACDW,IAAI,EAAE;MACJX,IAAI,EAAE,SAAS,CAAE;IACnB;EACF,CAAC;EACDY,UAAU,EAAE;IACVC,UAAU,EAAE,2GAA2G;IACvHC,EAAE,EAAE;MACFC,QAAQ,EAAE,0BAA0B;MACpCC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDC,EAAE,EAAE;MACFJ,QAAQ,EAAE,wBAAwB;MAClCC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDE,EAAE,EAAE;MACFL,QAAQ,EAAE,6BAA6B;MACvCC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDG,EAAE,EAAE;MACFN,QAAQ,EAAE,0BAA0B;MACpCC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDI,EAAE,EAAE;MACFP,QAAQ,EAAE,+BAA+B;MACzCC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDM,EAAE,EAAE;MACFR,QAAQ,EAAE,2BAA2B;MACrCC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDO,KAAK,EAAE;MACLT,QAAQ,EAAE,8BAA8B;MACxCE,UAAU,EAAE,GAAG;MACfD,UAAU,EAAE;IACd,CAAC;IACDS,KAAK,EAAE;MACLV,QAAQ,EAAE,+BAA+B;MACzCE,UAAU,EAAE,GAAG;MACfD,UAAU,EAAE;IACd;EACF,CAAC;EACDU,UAAU,EAAE;IACVC,SAAS,EAAE;MACTC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,eAAe,EAAE,uBAAuB;UAAE;UAC1CC,cAAc,EAAE,YAAY;UAAE;UAC9BC,YAAY,EAAE,sCAAsC;UACpDC,SAAS,EAAE,MAAM,CAAE;QACrB;MACF;IACF,CAAC;IACDC,YAAY,EAAE;MACZN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJM,eAAe,EAAE,MAAM;UACvBC,OAAO,EAAE;QACX;MACF;IACF,CAAC;IACDC,QAAQ,EAAE;MACRT,cAAc,EAAE;QACdC,IAAI,EAAE;UACJS,eAAe,EAAE,MAAM;UACvBR,eAAe,EAAE,uBAAuB;UAAE;UAC1CC,cAAc,EAAE,YAAY;UAAE;UAC9BQ,MAAM,EAAE,sCAAsC;UAAE;UAChDC,YAAY,EAAE,MAAM;UAAE;UACtB,SAAS,EAAE;YACTV,eAAe,EAAE,uBAAuB;YACxCW,SAAS,EAAE,kBAAkB,CAAE;UACjC,CAAC;UACDC,UAAU,EAAE,+CAA+C,CAAE;QAC/D;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTf,cAAc,EAAE;QACdC,IAAI,EAAE;UACJW,YAAY,EAAE,KAAK;UAAE;UACrBI,aAAa,EAAE,MAAM;UACrB5B,UAAU,EAAE,GAAG;UACfoB,OAAO,EAAE,WAAW;UACpBrB,QAAQ,EAAE,UAAU;UACpBF,UAAU,EAAE,8DAA8D;UAC1E,SAAS,EAAE;YACT4B,SAAS,EAAE,aAAa,CAAE;UAC5B,CAAC;UACDC,UAAU,EAAE,+CAA+C,CAAE;QAC/D,CAAC;QACDG,SAAS,EAAE;UACTzC,UAAU,EAAE,SAAS;UAAE;UACvB0C,KAAK,EAAE,SAAS;UAChBb,SAAS,EAAE,MAAM;UACjB,SAAS,EAAE;YACT7B,UAAU,EAAE,SAAS;YACrB6B,SAAS,EAAE;UACb;QACF,CAAC;QACDc,QAAQ,EAAE;UACRC,WAAW,EAAE,wBAAwB;UACrCF,KAAK,EAAE,SAAS;UAChB,SAAS,EAAE;YACTE,WAAW,EAAE,SAAS;YACtBlB,eAAe,EAAE;UACnB;QACF;MACF;IACF,CAAC;IACDmB,OAAO,EAAE;MACPrB,cAAc,EAAE;QACdC,IAAI,EAAE;UACJW,YAAY,EAAE,KAAK;UACnBxB,UAAU,EAAE,GAAG;UACfD,QAAQ,EAAE;QACZ;MACF;IACF;EACF,CAAC;EACDmC,WAAW,EAAE;IACXC,MAAM,EAAE;MACNC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,IAAI;MACRC,EAAE,EAAE;IACN;EACF;AACF,CAAC,CAAC;AAEF,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7CtG,SAAS,CAAC,MAAM;IACd;IACA,MAAMuG,QAAQ,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;MACrDA,OAAO,CAACvE,OAAO,CAACwE,KAAK,IAAI;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxBD,KAAK,CAACE,MAAM,CAACC,SAAS,CAAClF,GAAG,CAAC,UAAU,CAAC;QACxC;MACF,CAAC,CAAC;IACJ,CAAC,EAAE;MACDmF,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE;IACd,CAAC,CAAC;IAEF,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,IAAId,EAAE,UAAU,CAAC;IACxD,IAAIY,OAAO,EAAE;MACXT,QAAQ,CAACY,OAAO,CAACH,OAAO,CAAC;IAC3B;IAEA,OAAO,MAAM;MACX,IAAIA,OAAO,EAAE;QACXT,QAAQ,CAACa,SAAS,CAACJ,OAAO,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACZ,EAAE,CAAC,CAAC;EAER,oBACE9E,OAAA,CAACnB,GAAG;IACFiG,EAAE,EAAEA,EAAG;IACPiB,SAAS,EAAC,SAAS;IACnBC,EAAE,EAAE;MACFC,SAAS,EAAE;QAAEzB,EAAE,EAAE,MAAM;QAAEE,EAAE,EAAE;MAAQ,CAAC;MACtCwB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE;QAAE5B,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MAAE;MAC/B2B,EAAE,EAAE;QAAE7B,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;MAC3BnB,eAAe,EAAE;QAAEiB,EAAE,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAO,CAAC;MAC3C6B,QAAQ,EAAE;IACZ,CAAE;IAAAvB,QAAA,eAEF/E,OAAA,CAAClB,SAAS;MACRgG,EAAE,EAAE,GAAGA,EAAE,UAAW;MACpByB,QAAQ,EAAC,IAAI;MACbC,SAAS,EAAC,iBAAiB;MAC3BR,EAAE,EAAE;QACFS,KAAK,EAAE,MAAM;QACbC,EAAE,EAAE,MAAM;QACVL,EAAE,EAAE;UAAE7B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAC3BiC,OAAO,EAAE,CAAC;QACV9C,SAAS,EAAE,kBAAkB;QAC7BC,UAAU,EAAE,+CAA+C;QAAE;QAC7D,YAAY,EAAE;UACZ6C,OAAO,EAAE,CAAC;UACV9C,SAAS,EAAE;QACb;MACF,CAAE;MAAAkB,QAAA,EAEDA;IAAQ;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA7DIH,gBAAgB;AAAAmC,EAAA,GAAhBnC,gBAAgB;AA+DtB,SAASoC,UAAUA,CAAA,EAAG;EACpB,oBACEjH,OAAA,CAACL,eAAe;IAACuH,aAAa,EAAE,EAAG;IAAChD,KAAK,EAAC,SAAS;IAACyC,OAAO,EAAE,IAAK;IAAA5B,QAAA,eAChE/E,OAAA,CAACnB,GAAG;MAACmH,EAAE,EAAE;QACPC,SAAS,EAAE,OAAO;QAClB/C,eAAe,EAAE,SAAS;QAAE;QAC5BoD,QAAQ,EAAE,UAAU;QACpB9E,UAAU,EAAE,mDAAmD,CAAE;MACnE,CAAE;MAAAuD,QAAA,gBAEA/E,OAAA,CAACnB,GAAG;QACFmH,EAAE,EAAE;UACFM,QAAQ,EAAE,OAAO;UACjBa,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT9F,UAAU,EAAE;AACxB;AACA;AACA,aAAa;UACD+F,aAAa,EAAE;QACjB;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAKJ/G,OAAA,CAACnB,GAAG;QAACmH,EAAE,EAAE;UAAEM,QAAQ,EAAE,OAAO;UAAEa,GAAG,EAAE,EAAE;UAAEE,KAAK,EAAE,EAAE;UAAEG,MAAM,EAAE;QAAK,CAAE;QAAAzC,QAAA,eAC/D/E,OAAA,CAACP,WAAW;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEN/G,OAAA,CAACnB,GAAG;QAACmH,EAAE,EAAE;UAAEM,QAAQ,EAAE,UAAU;UAAEmB,MAAM,EAAE;QAAO,CAAE;QAAA1C,QAAA,eAChD/E,OAAA,CAACN,oBAAoB;UAACgI,SAAS,EAAE,GAAI;UAACxD,KAAK,EAAC,SAAS;UAACyC,OAAO,EAAE,IAAK;UAAA5B,QAAA,eAClE/E,OAAA,CAACjB,MAAM;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACN/G,OAAA,CAACnB,GAAG;QACFkH,SAAS,EAAC,MAAM;QAChBC,EAAE,EAAE;UACFM,QAAQ,EAAE,UAAU;UACpBqB,cAAc,EAAE,QAAQ;UACxBC,EAAE,EAAE,MAAM;UAAE;UACZ,wBAAwB,EAAE;YACxBnB,KAAK,EAAE;UACT,CAAC;UACD,8BAA8B,EAAE;YAC9BjF,UAAU,EAAE;UACd,CAAC;UACD,8BAA8B,EAAE;YAC9BA,UAAU,EAAE,SAAS;YACrBoC,YAAY,EAAE,KAAK;YACnB,SAAS,EAAE;cACTpC,UAAU,EAAE;YACd;UACF;QACF,CAAE;QAAAuD,QAAA,gBAEF/E,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,OAAO;UAAAC,QAAA,eAC1B/E,OAAA,CAAChB,KAAK;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACnB/G,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,WAAW;UAAAC,QAAA,eAC9B/E,OAAA,CAACf,SAAS;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACnB/G,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,YAAY;UAAAC,QAAA,eAC/B/E,OAAA,CAACd,UAAU;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACnB/G,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,UAAU;UAAAC,QAAA,eAC7B/E,OAAA,CAACb,QAAQ;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACnB/G,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,QAAQ;UAAAC,QAAA,eAC3B/E,OAAA,CAACZ,MAAM;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eACnB/G,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,gBAAgB;UAAAC,QAAA,eACnC/E,OAAA,CAACX,cAAc;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACnB/G,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,cAAc;UAAAC,QAAA,eACjC/E,OAAA,CAACV,YAAY;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACnB/G,OAAA,CAAC6E,gBAAgB;UAACC,EAAE,EAAC,SAAS;UAAAC,QAAA,eAC5B/E,OAAA,CAACT,OAAO;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAGJ/G,OAAA,CAACR,kBAAkB;QAAAoH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAEtB;AAACc,GAAA,GAzFQZ,UAAU;AA2FnB,SAASa,GAAGA,CAAA,EAAG;EACb,oBACE9H,OAAA,CAACpB,aAAa;IAACoC,KAAK,EAAEA,KAAM;IAAA+D,QAAA,eAC1B/E,OAAA,CAACiH,UAAU;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACgB,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAd,EAAA,EAAAa,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAhB,EAAA;AAAAgB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}