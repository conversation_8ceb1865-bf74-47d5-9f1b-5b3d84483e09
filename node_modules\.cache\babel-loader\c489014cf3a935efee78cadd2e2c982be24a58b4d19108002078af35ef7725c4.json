{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\HyperspeedBackground.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HyperspeedBackground = ({\n  children,\n  intensity = 1,\n  color = '#007AFF'\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    let animationId;\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Hyperspeed particles\n    const particles = [];\n    const numParticles = 100 * intensity;\n    class Particle {\n      constructor() {\n        this.reset();\n      }\n      reset() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.z = Math.random() * 1000;\n        this.prevX = this.x;\n        this.prevY = this.y;\n      }\n      update() {\n        this.prevX = this.x;\n        this.prevY = this.y;\n        this.z -= 10 * intensity;\n        if (this.z <= 0) {\n          this.reset();\n          return;\n        }\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        this.x = centerX + (this.x - centerX) * (1000 / this.z);\n        this.y = centerY + (this.y - centerY) * (1000 / this.z);\n      }\n      draw() {\n        const opacity = Math.max(0, 1 - this.z / 1000);\n        const size = Math.max(1, (1000 - this.z) / 200);\n        ctx.strokeStyle = `${color}${Math.floor(opacity * 255).toString(16).padStart(2, '0')}`;\n        ctx.lineWidth = size;\n        ctx.beginPath();\n        ctx.moveTo(this.prevX, this.prevY);\n        ctx.lineTo(this.x, this.y);\n        ctx.stroke();\n\n        // Add glow effect\n        ctx.shadowColor = color;\n        ctx.shadowBlur = size * 2;\n        ctx.fillStyle = `${color}${Math.floor(opacity * 128).toString(16).padStart(2, '0')}`;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, size / 2, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.shadowBlur = 0;\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < numParticles; i++) {\n      particles.push(new Particle());\n    }\n    const animate = () => {\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      animationId = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [intensity, color]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: 0,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(HyperspeedBackground, \"X5bd7Q1XXg1keIMflMhOltk4wyU=\");\n_c = HyperspeedBackground;\nexport default HyperspeedBackground;\nvar _c;\n$RefreshReg$(_c, \"HyperspeedBackground\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "jsxDEV", "_jsxDEV", "HyperspeedBackground", "children", "intensity", "color", "_s", "canvasRef", "animationRef", "canvas", "current", "ctx", "getContext", "animationId", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "particles", "numParticles", "Particle", "constructor", "reset", "x", "Math", "random", "y", "z", "prevX", "prevY", "update", "centerX", "centerY", "draw", "opacity", "max", "size", "strokeStyle", "floor", "toString", "padStart", "lineWidth", "beginPath", "moveTo", "lineTo", "stroke", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "fillStyle", "arc", "PI", "fill", "i", "push", "animate", "fillRect", "for<PERSON>ach", "particle", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "sx", "position", "overflow", "ref", "style", "top", "left", "zIndex", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/HyperspeedBackground.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\n\nconst HyperspeedBackground = ({ children, intensity = 1, color = '#007AFF' }) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationId;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Hyperspeed particles\n    const particles = [];\n    const numParticles = 100 * intensity;\n\n    class Particle {\n      constructor() {\n        this.reset();\n      }\n\n      reset() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.z = Math.random() * 1000;\n        this.prevX = this.x;\n        this.prevY = this.y;\n      }\n\n      update() {\n        this.prevX = this.x;\n        this.prevY = this.y;\n\n        this.z -= 10 * intensity;\n\n        if (this.z <= 0) {\n          this.reset();\n          return;\n        }\n\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n\n        this.x = centerX + (this.x - centerX) * (1000 / this.z);\n        this.y = centerY + (this.y - centerY) * (1000 / this.z);\n      }\n\n      draw() {\n        const opacity = Math.max(0, 1 - this.z / 1000);\n        const size = Math.max(1, (1000 - this.z) / 200);\n\n        ctx.strokeStyle = `${color}${Math.floor(opacity * 255).toString(16).padStart(2, '0')}`;\n        ctx.lineWidth = size;\n        ctx.beginPath();\n        ctx.moveTo(this.prevX, this.prevY);\n        ctx.lineTo(this.x, this.y);\n        ctx.stroke();\n\n        // Add glow effect\n        ctx.shadowColor = color;\n        ctx.shadowBlur = size * 2;\n        ctx.fillStyle = `${color}${Math.floor(opacity * 128).toString(16).padStart(2, '0')}`;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, size / 2, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.shadowBlur = 0;\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < numParticles; i++) {\n      particles.push(new Particle());\n    }\n\n    const animate = () => {\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [intensity, color]);\n\n  return (\n    <Box sx={{ position: 'relative', overflow: 'hidden' }}>\n      <canvas\n        ref={canvasRef}\n        style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          zIndex: 0,\n          pointerEvents: 'none',\n        }}\n      />\n      <Box sx={{ position: 'relative', zIndex: 1 }}>\n        {children}\n      </Box>\n    </Box>\n  );\n};\n\nexport default HyperspeedBackground;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG,CAAC;EAAEC,KAAK,GAAG;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAMC,SAAS,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMU,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMY,MAAM,GAAGF,SAAS,CAACG,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAIC,WAAW;IAEf,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBL,MAAM,CAACM,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCR,MAAM,CAACS,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,SAAS,GAAG,EAAE;IACpB,MAAMC,YAAY,GAAG,GAAG,GAAGlB,SAAS;IAEpC,MAAMmB,QAAQ,CAAC;MACbC,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACC,KAAK,CAAC,CAAC;MACd;MAEAA,KAAKA,CAAA,EAAG;QACN,IAAI,CAACC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGnB,MAAM,CAACM,KAAK;QACrC,IAAI,CAACc,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGnB,MAAM,CAACS,MAAM;QACtC,IAAI,CAACY,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;QAC7B,IAAI,CAACG,KAAK,GAAG,IAAI,CAACL,CAAC;QACnB,IAAI,CAACM,KAAK,GAAG,IAAI,CAACH,CAAC;MACrB;MAEAI,MAAMA,CAAA,EAAG;QACP,IAAI,CAACF,KAAK,GAAG,IAAI,CAACL,CAAC;QACnB,IAAI,CAACM,KAAK,GAAG,IAAI,CAACH,CAAC;QAEnB,IAAI,CAACC,CAAC,IAAI,EAAE,GAAG1B,SAAS;QAExB,IAAI,IAAI,CAAC0B,CAAC,IAAI,CAAC,EAAE;UACf,IAAI,CAACL,KAAK,CAAC,CAAC;UACZ;QACF;QAEA,MAAMS,OAAO,GAAGzB,MAAM,CAACM,KAAK,GAAG,CAAC;QAChC,MAAMoB,OAAO,GAAG1B,MAAM,CAACS,MAAM,GAAG,CAAC;QAEjC,IAAI,CAACQ,CAAC,GAAGQ,OAAO,GAAG,CAAC,IAAI,CAACR,CAAC,GAAGQ,OAAO,KAAK,IAAI,GAAG,IAAI,CAACJ,CAAC,CAAC;QACvD,IAAI,CAACD,CAAC,GAAGM,OAAO,GAAG,CAAC,IAAI,CAACN,CAAC,GAAGM,OAAO,KAAK,IAAI,GAAG,IAAI,CAACL,CAAC,CAAC;MACzD;MAEAM,IAAIA,CAAA,EAAG;QACL,MAAMC,OAAO,GAAGV,IAAI,CAACW,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAACR,CAAC,GAAG,IAAI,CAAC;QAC9C,MAAMS,IAAI,GAAGZ,IAAI,CAACW,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAACR,CAAC,IAAI,GAAG,CAAC;QAE/CnB,GAAG,CAAC6B,WAAW,GAAG,GAAGnC,KAAK,GAAGsB,IAAI,CAACc,KAAK,CAACJ,OAAO,GAAG,GAAG,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QACtFhC,GAAG,CAACiC,SAAS,GAAGL,IAAI;QACpB5B,GAAG,CAACkC,SAAS,CAAC,CAAC;QACflC,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACf,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;QAClCrB,GAAG,CAACoC,MAAM,CAAC,IAAI,CAACrB,CAAC,EAAE,IAAI,CAACG,CAAC,CAAC;QAC1BlB,GAAG,CAACqC,MAAM,CAAC,CAAC;;QAEZ;QACArC,GAAG,CAACsC,WAAW,GAAG5C,KAAK;QACvBM,GAAG,CAACuC,UAAU,GAAGX,IAAI,GAAG,CAAC;QACzB5B,GAAG,CAACwC,SAAS,GAAG,GAAG9C,KAAK,GAAGsB,IAAI,CAACc,KAAK,CAACJ,OAAO,GAAG,GAAG,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QACpFhC,GAAG,CAACkC,SAAS,CAAC,CAAC;QACflC,GAAG,CAACyC,GAAG,CAAC,IAAI,CAAC1B,CAAC,EAAE,IAAI,CAACG,CAAC,EAAEU,IAAI,GAAG,CAAC,EAAE,CAAC,EAAEZ,IAAI,CAAC0B,EAAE,GAAG,CAAC,CAAC;QACjD1C,GAAG,CAAC2C,IAAI,CAAC,CAAC;QACV3C,GAAG,CAACuC,UAAU,GAAG,CAAC;MACpB;IACF;;IAEA;IACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,YAAY,EAAEiC,CAAC,EAAE,EAAE;MACrClC,SAAS,CAACmC,IAAI,CAAC,IAAIjC,QAAQ,CAAC,CAAC,CAAC;IAChC;IAEA,MAAMkC,OAAO,GAAGA,CAAA,KAAM;MACpB9C,GAAG,CAACwC,SAAS,GAAG,oBAAoB;MACpCxC,GAAG,CAAC+C,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEjD,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACS,MAAM,CAAC;MAE/CG,SAAS,CAACsC,OAAO,CAACC,QAAQ,IAAI;QAC5BA,QAAQ,CAAC3B,MAAM,CAAC,CAAC;QACjB2B,QAAQ,CAACxB,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;MAEFvB,WAAW,GAAGgD,qBAAqB,CAACJ,OAAO,CAAC;IAC9C,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXzC,MAAM,CAAC8C,mBAAmB,CAAC,QAAQ,EAAEhD,YAAY,CAAC;MAClD,IAAID,WAAW,EAAE;QACfkD,oBAAoB,CAAClD,WAAW,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAACT,SAAS,EAAEC,KAAK,CAAC,CAAC;EAEtB,oBACEJ,OAAA,CAACF,GAAG;IAACiE,EAAE,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAA/D,QAAA,gBACpDF,OAAA;MACEkE,GAAG,EAAE5D,SAAU;MACf6D,KAAK,EAAE;QACLH,QAAQ,EAAE,UAAU;QACpBI,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPvD,KAAK,EAAE,MAAM;QACbG,MAAM,EAAE,MAAM;QACdqD,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE;MACjB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF3E,OAAA,CAACF,GAAG;MAACiE,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEM,MAAM,EAAE;MAAE,CAAE;MAAApE,QAAA,EAC1CA;IAAQ;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CA1HIJ,oBAAoB;AAAA2E,EAAA,GAApB3E,oBAAoB;AA4H1B,eAAeA,oBAAoB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}