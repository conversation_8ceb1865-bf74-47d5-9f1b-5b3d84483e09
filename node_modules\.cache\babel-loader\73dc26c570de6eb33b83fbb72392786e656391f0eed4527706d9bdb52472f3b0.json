{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\AuroraBackground.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuroraBackground = ({\n  children,\n  colors = ['#007AFF', '#5AC8FA', '#FF9500'],\n  opacity = 0.15\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    let animationId;\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Aurora waves\n    const waves = [];\n    const numWaves = 3;\n    class Wave {\n      constructor(color, speed, amplitude, frequency) {\n        this.color = color;\n        this.speed = speed;\n        this.amplitude = amplitude;\n        this.frequency = frequency;\n        this.offset = Math.random() * Math.PI * 2;\n        this.time = 0;\n      }\n      update() {\n        this.time += this.speed;\n      }\n      draw() {\n        ctx.save();\n\n        // Create gradient\n        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);\n        gradient.addColorStop(0, `${this.color}00`);\n        gradient.addColorStop(0.5, `${this.color}15`);\n        gradient.addColorStop(1, `${this.color}00`);\n        ctx.fillStyle = gradient;\n        ctx.globalCompositeOperation = 'screen';\n        ctx.beginPath();\n        ctx.moveTo(0, canvas.height);\n        for (let x = 0; x <= canvas.width; x += 5) {\n          const y = canvas.height / 2 + Math.sin(x * this.frequency + this.time + this.offset) * this.amplitude + Math.sin(x * this.frequency * 0.5 + this.time * 0.7 + this.offset) * this.amplitude * 0.5;\n          ctx.lineTo(x, y);\n        }\n        ctx.lineTo(canvas.width, canvas.height);\n        ctx.closePath();\n        ctx.fill();\n        ctx.restore();\n      }\n    }\n\n    // Initialize waves\n    colors.forEach((color, index) => {\n      waves.push(new Wave(color, 0.02 + index * 0.01, 50 + index * 30, 0.005 + index * 0.002));\n    });\n\n    // Floating particles\n    const particles = [];\n    const numParticles = 50;\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.vx = (Math.random() - 0.5) * 0.5;\n        this.vy = (Math.random() - 0.5) * 0.5;\n        this.size = Math.random() * 3 + 1;\n        this.opacity = Math.random() * 0.5 + 0.2;\n        this.color = colors[Math.floor(Math.random() * colors.length)];\n      }\n      update() {\n        this.x += this.vx;\n        this.y += this.vy;\n        if (this.x < 0 || this.x > canvas.width) this.vx *= -1;\n        if (this.y < 0 || this.y > canvas.height) this.vy *= -1;\n        this.opacity += (Math.random() - 0.5) * 0.02;\n        this.opacity = Math.max(0.1, Math.min(0.7, this.opacity));\n      }\n      draw() {\n        ctx.save();\n        ctx.globalAlpha = this.opacity;\n        ctx.fillStyle = this.color;\n        ctx.shadowColor = this.color;\n        ctx.shadowBlur = this.size * 2;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.restore();\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < numParticles; i++) {\n      particles.push(new Particle());\n    }\n    const animate = () => {\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      waves.forEach(wave => {\n        wave.update();\n        wave.draw();\n      });\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      animationId = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [colors]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: 0,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(AuroraBackground, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = AuroraBackground;\nexport default AuroraBackground;\nvar _c;\n$RefreshReg$(_c, \"AuroraBackground\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "jsxDEV", "_jsxDEV", "AuroraBackground", "children", "colors", "opacity", "_s", "canvasRef", "canvas", "current", "ctx", "getContext", "animationId", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "waves", "numWaves", "Wave", "constructor", "color", "speed", "amplitude", "frequency", "offset", "Math", "random", "PI", "time", "update", "draw", "save", "gradient", "createLinearGradient", "addColorStop", "fillStyle", "globalCompositeOperation", "beginPath", "moveTo", "x", "y", "sin", "lineTo", "closePath", "fill", "restore", "for<PERSON>ach", "index", "push", "particles", "numParticles", "Particle", "vx", "vy", "size", "floor", "length", "max", "min", "globalAlpha", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "arc", "i", "animate", "fillRect", "wave", "particle", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "sx", "position", "overflow", "ref", "style", "top", "left", "zIndex", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/AuroraBackground.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\n\nconst AuroraBackground = ({ children, colors = ['#007AFF', '#5AC8FA', '#FF9500'], opacity = 0.15 }) => {\n  const canvasRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationId;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Aurora waves\n    const waves = [];\n    const numWaves = 3;\n\n    class Wave {\n      constructor(color, speed, amplitude, frequency) {\n        this.color = color;\n        this.speed = speed;\n        this.amplitude = amplitude;\n        this.frequency = frequency;\n        this.offset = Math.random() * Math.PI * 2;\n        this.time = 0;\n      }\n\n      update() {\n        this.time += this.speed;\n      }\n\n      draw() {\n        ctx.save();\n        \n        // Create gradient\n        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);\n        gradient.addColorStop(0, `${this.color}00`);\n        gradient.addColorStop(0.5, `${this.color}15`);\n        gradient.addColorStop(1, `${this.color}00`);\n        \n        ctx.fillStyle = gradient;\n        ctx.globalCompositeOperation = 'screen';\n        \n        ctx.beginPath();\n        ctx.moveTo(0, canvas.height);\n        \n        for (let x = 0; x <= canvas.width; x += 5) {\n          const y = canvas.height / 2 + \n                   Math.sin((x * this.frequency + this.time + this.offset)) * this.amplitude +\n                   Math.sin((x * this.frequency * 0.5 + this.time * 0.7 + this.offset)) * this.amplitude * 0.5;\n          ctx.lineTo(x, y);\n        }\n        \n        ctx.lineTo(canvas.width, canvas.height);\n        ctx.closePath();\n        ctx.fill();\n        \n        ctx.restore();\n      }\n    }\n\n    // Initialize waves\n    colors.forEach((color, index) => {\n      waves.push(new Wave(\n        color,\n        0.02 + index * 0.01,\n        50 + index * 30,\n        0.005 + index * 0.002\n      ));\n    });\n\n    // Floating particles\n    const particles = [];\n    const numParticles = 50;\n\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.vx = (Math.random() - 0.5) * 0.5;\n        this.vy = (Math.random() - 0.5) * 0.5;\n        this.size = Math.random() * 3 + 1;\n        this.opacity = Math.random() * 0.5 + 0.2;\n        this.color = colors[Math.floor(Math.random() * colors.length)];\n      }\n\n      update() {\n        this.x += this.vx;\n        this.y += this.vy;\n\n        if (this.x < 0 || this.x > canvas.width) this.vx *= -1;\n        if (this.y < 0 || this.y > canvas.height) this.vy *= -1;\n\n        this.opacity += (Math.random() - 0.5) * 0.02;\n        this.opacity = Math.max(0.1, Math.min(0.7, this.opacity));\n      }\n\n      draw() {\n        ctx.save();\n        ctx.globalAlpha = this.opacity;\n        ctx.fillStyle = this.color;\n        ctx.shadowColor = this.color;\n        ctx.shadowBlur = this.size * 2;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.restore();\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < numParticles; i++) {\n      particles.push(new Particle());\n    }\n\n    const animate = () => {\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      waves.forEach(wave => {\n        wave.update();\n        wave.draw();\n      });\n\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [colors]);\n\n  return (\n    <Box sx={{ position: 'relative', overflow: 'hidden' }}>\n      <canvas\n        ref={canvasRef}\n        style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          zIndex: 0,\n          pointerEvents: 'none',\n        }}\n      />\n      <Box sx={{ position: 'relative', zIndex: 1 }}>\n        {children}\n      </Box>\n    </Box>\n  );\n};\n\nexport default AuroraBackground;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAAEC,OAAO,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrG,MAAMC,SAAS,GAAGT,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,MAAMW,MAAM,GAAGD,SAAS,CAACE,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAIC,WAAW;IAEf,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBL,MAAM,CAACM,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCR,MAAM,CAACS,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,KAAK,GAAG,EAAE;IAChB,MAAMC,QAAQ,GAAG,CAAC;IAElB,MAAMC,IAAI,CAAC;MACTC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;QAC9C,IAAI,CAACH,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACC,MAAM,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGD,IAAI,CAACE,EAAE,GAAG,CAAC;QACzC,IAAI,CAACC,IAAI,GAAG,CAAC;MACf;MAEAC,MAAMA,CAAA,EAAG;QACP,IAAI,CAACD,IAAI,IAAI,IAAI,CAACP,KAAK;MACzB;MAEAS,IAAIA,CAAA,EAAG;QACLxB,GAAG,CAACyB,IAAI,CAAC,CAAC;;QAEV;QACA,MAAMC,QAAQ,GAAG1B,GAAG,CAAC2B,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE7B,MAAM,CAACS,MAAM,CAAC;QACjEmB,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,GAAG,IAAI,CAACd,KAAK,IAAI,CAAC;QAC3CY,QAAQ,CAACE,YAAY,CAAC,GAAG,EAAE,GAAG,IAAI,CAACd,KAAK,IAAI,CAAC;QAC7CY,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,GAAG,IAAI,CAACd,KAAK,IAAI,CAAC;QAE3Cd,GAAG,CAAC6B,SAAS,GAAGH,QAAQ;QACxB1B,GAAG,CAAC8B,wBAAwB,GAAG,QAAQ;QAEvC9B,GAAG,CAAC+B,SAAS,CAAC,CAAC;QACf/B,GAAG,CAACgC,MAAM,CAAC,CAAC,EAAElC,MAAM,CAACS,MAAM,CAAC;QAE5B,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAInC,MAAM,CAACM,KAAK,EAAE6B,CAAC,IAAI,CAAC,EAAE;UACzC,MAAMC,CAAC,GAAGpC,MAAM,CAACS,MAAM,GAAG,CAAC,GAClBY,IAAI,CAACgB,GAAG,CAAEF,CAAC,GAAG,IAAI,CAAChB,SAAS,GAAG,IAAI,CAACK,IAAI,GAAG,IAAI,CAACJ,MAAO,CAAC,GAAG,IAAI,CAACF,SAAS,GACzEG,IAAI,CAACgB,GAAG,CAAEF,CAAC,GAAG,IAAI,CAAChB,SAAS,GAAG,GAAG,GAAG,IAAI,CAACK,IAAI,GAAG,GAAG,GAAG,IAAI,CAACJ,MAAO,CAAC,GAAG,IAAI,CAACF,SAAS,GAAG,GAAG;UACpGhB,GAAG,CAACoC,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;QAClB;QAEAlC,GAAG,CAACoC,MAAM,CAACtC,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACS,MAAM,CAAC;QACvCP,GAAG,CAACqC,SAAS,CAAC,CAAC;QACfrC,GAAG,CAACsC,IAAI,CAAC,CAAC;QAEVtC,GAAG,CAACuC,OAAO,CAAC,CAAC;MACf;IACF;;IAEA;IACA7C,MAAM,CAAC8C,OAAO,CAAC,CAAC1B,KAAK,EAAE2B,KAAK,KAAK;MAC/B/B,KAAK,CAACgC,IAAI,CAAC,IAAI9B,IAAI,CACjBE,KAAK,EACL,IAAI,GAAG2B,KAAK,GAAG,IAAI,EACnB,EAAE,GAAGA,KAAK,GAAG,EAAE,EACf,KAAK,GAAGA,KAAK,GAAG,KAClB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAME,SAAS,GAAG,EAAE;IACpB,MAAMC,YAAY,GAAG,EAAE;IAEvB,MAAMC,QAAQ,CAAC;MACbhC,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACoB,CAAC,GAAGd,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGtB,MAAM,CAACM,KAAK;QACrC,IAAI,CAAC8B,CAAC,GAAGf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGtB,MAAM,CAACS,MAAM;QACtC,IAAI,CAACuC,EAAE,GAAG,CAAC3B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACrC,IAAI,CAAC2B,EAAE,GAAG,CAAC5B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACrC,IAAI,CAAC4B,IAAI,GAAG7B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACjC,IAAI,CAACzB,OAAO,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QACxC,IAAI,CAACN,KAAK,GAAGpB,MAAM,CAACyB,IAAI,CAAC8B,KAAK,CAAC9B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG1B,MAAM,CAACwD,MAAM,CAAC,CAAC;MAChE;MAEA3B,MAAMA,CAAA,EAAG;QACP,IAAI,CAACU,CAAC,IAAI,IAAI,CAACa,EAAE;QACjB,IAAI,CAACZ,CAAC,IAAI,IAAI,CAACa,EAAE;QAEjB,IAAI,IAAI,CAACd,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,CAAC,GAAGnC,MAAM,CAACM,KAAK,EAAE,IAAI,CAAC0C,EAAE,IAAI,CAAC,CAAC;QACtD,IAAI,IAAI,CAACZ,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,CAAC,GAAGpC,MAAM,CAACS,MAAM,EAAE,IAAI,CAACwC,EAAE,IAAI,CAAC,CAAC;QAEvD,IAAI,CAACpD,OAAO,IAAI,CAACwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;QAC5C,IAAI,CAACzB,OAAO,GAAGwB,IAAI,CAACgC,GAAG,CAAC,GAAG,EAAEhC,IAAI,CAACiC,GAAG,CAAC,GAAG,EAAE,IAAI,CAACzD,OAAO,CAAC,CAAC;MAC3D;MAEA6B,IAAIA,CAAA,EAAG;QACLxB,GAAG,CAACyB,IAAI,CAAC,CAAC;QACVzB,GAAG,CAACqD,WAAW,GAAG,IAAI,CAAC1D,OAAO;QAC9BK,GAAG,CAAC6B,SAAS,GAAG,IAAI,CAACf,KAAK;QAC1Bd,GAAG,CAACsD,WAAW,GAAG,IAAI,CAACxC,KAAK;QAC5Bd,GAAG,CAACuD,UAAU,GAAG,IAAI,CAACP,IAAI,GAAG,CAAC;QAC9BhD,GAAG,CAAC+B,SAAS,CAAC,CAAC;QACf/B,GAAG,CAACwD,GAAG,CAAC,IAAI,CAACvB,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACc,IAAI,EAAE,CAAC,EAAE7B,IAAI,CAACE,EAAE,GAAG,CAAC,CAAC;QAClDrB,GAAG,CAACsC,IAAI,CAAC,CAAC;QACVtC,GAAG,CAACuC,OAAO,CAAC,CAAC;MACf;IACF;;IAEA;IACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,EAAEa,CAAC,EAAE,EAAE;MACrCd,SAAS,CAACD,IAAI,CAAC,IAAIG,QAAQ,CAAC,CAAC,CAAC;IAChC;IAEA,MAAMa,OAAO,GAAGA,CAAA,KAAM;MACpB1D,GAAG,CAAC6B,SAAS,GAAG,qBAAqB;MACrC7B,GAAG,CAAC2D,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE7D,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACS,MAAM,CAAC;MAE/CG,KAAK,CAAC8B,OAAO,CAACoB,IAAI,IAAI;QACpBA,IAAI,CAACrC,MAAM,CAAC,CAAC;QACbqC,IAAI,CAACpC,IAAI,CAAC,CAAC;MACb,CAAC,CAAC;MAEFmB,SAAS,CAACH,OAAO,CAACqB,QAAQ,IAAI;QAC5BA,QAAQ,CAACtC,MAAM,CAAC,CAAC;QACjBsC,QAAQ,CAACrC,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;MAEFtB,WAAW,GAAG4D,qBAAqB,CAACJ,OAAO,CAAC;IAC9C,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXrD,MAAM,CAAC0D,mBAAmB,CAAC,QAAQ,EAAE5D,YAAY,CAAC;MAClD,IAAID,WAAW,EAAE;QACf8D,oBAAoB,CAAC9D,WAAW,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;EAEZ,oBACEH,OAAA,CAACF,GAAG;IAAC4E,EAAE,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAA1E,QAAA,gBACpDF,OAAA;MACE6E,GAAG,EAAEvE,SAAU;MACfwE,KAAK,EAAE;QACLH,QAAQ,EAAE,UAAU;QACpBI,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPnE,KAAK,EAAE,MAAM;QACbG,MAAM,EAAE,MAAM;QACdiE,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE;MACjB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFtF,OAAA,CAACF,GAAG;MAAC4E,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEM,MAAM,EAAE;MAAE,CAAE;MAAA/E,QAAA,EAC1CA;IAAQ;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjF,EAAA,CAtKIJ,gBAAgB;AAAAsF,EAAA,GAAhBtF,gBAAgB;AAwKtB,eAAeA,gBAAgB;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}