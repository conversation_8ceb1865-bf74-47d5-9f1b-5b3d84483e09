import React, { useState, useEffect } from 'react';
import { Typography, Box } from '@mui/material';

const RotatingText = ({ 
  words = [], 
  variant = 'h6', 
  className = '', 
  sx = {}, 
  speed = 2000,
  prefix = '',
  suffix = '',
  ...props 
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (words.length <= 1) return;

    const interval = setInterval(() => {
      setIsAnimating(true);
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length);
        setIsAnimating(false);
      }, 300); // Half of the animation duration
      
    }, speed);

    return () => clearInterval(interval);
  }, [words, speed]);

  if (!words.length) return null;

  return (
    <Box sx={{ display: 'inline-flex', alignItems: 'center', overflow: 'hidden' }}>
      {prefix && (
        <Typography variant={variant} className={className} sx={sx} {...props}>
          {prefix}
        </Typography>
      )}
      
      <Box
        sx={{
          position: 'relative',
          display: 'inline-block',
          minWidth: '120px', // Adjust based on your content
          textAlign: 'center',
        }}
      >
        <Typography
          variant={variant}
          className={className}
          sx={{
            background: 'linear-gradient(45deg, #007AFF, #5AC8FA, #FF9500)',
            backgroundSize: '200% 200%',
            animation: 'gradient-shift 3s ease infinite',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            transform: isAnimating ? 'rotateX(90deg)' : 'rotateX(0deg)',
            transition: 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            transformStyle: 'preserve-3d',
            '@keyframes gradient-shift': {
              '0%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
              '100%': { backgroundPosition: '0% 50%' }
            },
            ...sx
          }}
          {...props}
        >
          {words[currentIndex]}
        </Typography>
      </Box>
      
      {suffix && (
        <Typography variant={variant} className={className} sx={sx} {...props}>
          {suffix}
        </Typography>
      )}
    </Box>
  );
};

export default RotatingText;
