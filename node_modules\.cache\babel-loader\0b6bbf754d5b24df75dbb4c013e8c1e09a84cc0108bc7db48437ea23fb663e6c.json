{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\About.js\";\nimport React from 'react';\nimport Container from '@mui/material/Container';\nimport Typography from '@mui/material/Typography';\nimport Box from '@mui/material/Box';\nimport Paper from '@mui/material/Paper';\nimport Grid from '@mui/material/Grid';\nimport Avatar from '@mui/material/Avatar';\nimport { Divider, Chip } from '@mui/material';\nimport CodeIcon from '@mui/icons-material/Code';\nimport SecurityIcon from '@mui/icons-material/Security';\nimport GroupsIcon from '@mui/icons-material/Groups';\nimport SchoolIcon from '@mui/icons-material/School';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport EmailIcon from '@mui/icons-material/Email';\nimport PhoneIcon from '@mui/icons-material/Phone';\nimport { AuroraBackground, RotatingText } from './effects';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction About() {\n  const highlights = [{\n    icon: /*#__PURE__*/_jsxDEV(CodeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this),\n    title: \"AI/ML Expert\",\n    description: \"Advanced projects in machine learning and computer vision\",\n    color: \"#6366f1\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    title: \"Cybersecurity\",\n    description: \"CTF champion with penetration testing expertise\",\n    color: \"#ef4444\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(GroupsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this),\n    title: \"Leadership\",\n    description: \"Active in multiple tech clubs and organizations\",\n    color: \"#10b981\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SchoolIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this),\n    title: \"Published Author\",\n    description: \"Author of cybersecurity book on Amazon\",\n    color: \"#f59e0b\"\n  }];\n  const contactInfo = [{\n    icon: /*#__PURE__*/_jsxDEV(LocationOnIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this),\n    text: \"Egattur Chennai, Tamil Nadu\",\n    label: \"Location\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(EmailIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this),\n    text: \"<EMAIL>\",\n    label: \"Email\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(PhoneIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this),\n    text: \"91+ 8939776542\",\n    label: \"Phone\"\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(AuroraBackground, {\n      colors: ['#007AFF', '#5AC8FA', '#FF9500'],\n      opacity: 0.08,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 0,\n            className: \"glass-effect\",\n            sx: {\n              p: {\n                xs: 3,\n                md: 4\n              },\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 4\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  width: 80,\n                  height: 80,\n                  mr: 3,\n                  background: 'linear-gradient(135deg, #6366f1, #8b5cf6)',\n                  fontSize: '2rem',\n                  fontWeight: 'bold'\n                },\n                children: \"JP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  className: \"gradient-text\",\n                  sx: {\n                    mb: 1,\n                    fontWeight: 700\n                  },\n                  children: \"Joel Prince\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(RotatingText, {\n                  words: ['Computer Science Student at VIT Chennai', 'AI/ML Enthusiast & Researcher', 'Cybersecurity Expert & CTF Champion', 'Full-Stack Developer', 'Published Author & Tech Leader'],\n                  variant: \"h6\",\n                  sx: {\n                    mb: 1,\n                    color: 'text.secondary'\n                  },\n                  speed: 3000\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1,\n                    flexWrap: 'wrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"HackClub VIT\",\n                    size: \"small\",\n                    sx: {\n                      background: 'linear-gradient(45deg, #6366f1, #8b5cf6)',\n                      color: 'white'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Linux Club VIT\",\n                    size: \"small\",\n                    sx: {\n                      background: 'linear-gradient(45deg, #06b6d4, #10b981)',\n                      color: 'white'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                lineHeight: 1.8,\n                mb: 4,\n                fontSize: '1.1rem'\n              },\n              children: [\"I'm a passionate Computer Science student specializing in \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"AI/ML\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 73\n              }, this), \", \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"cybersecurity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 97\n              }, this), \", and \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"full-stack development\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this), \". My portfolio includes cutting-edge projects like MarketAnalyzerX for stock market analysis, an Autonomous Tactical Navigation System with 94% threat detection accuracy, and reinforcement learning models for DDoS defense.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                lineHeight: 1.8,\n                mb: 4,\n                fontSize: '1.1rem'\n              },\n              children: [\"As a \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"CTF champion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 20\n              }, this), \" (2nd place CryptoClash CTF 2025, 77th rank HackerEarth CTF Asia 2025) and \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"published author\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), \" of \\\"From Code To Combat: The Silent War Where Algorithms Strike First\\\", I bring both theoretical knowledge and practical experience to every project.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Paper, {\n              className: \"glass-effect\",\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                className: \"gradient-text\",\n                sx: {\n                  mb: 3\n                },\n                children: \"Contact Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 15\n              }, this), contactInfo.map((info, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    color: 'primary.main',\n                    mr: 2\n                  },\n                  children: info.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: info.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              className: \"glass-effect\",\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                className: \"gradient-text\",\n                sx: {\n                  mb: 3\n                },\n                children: \"Key Highlights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 2\n                },\n                children: highlights.map((highlight, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    p: 2,\n                    borderRadius: 2,\n                    background: `${highlight.color}15`,\n                    border: `1px solid ${highlight.color}30`,\n                    transition: 'all 0.3s ease',\n                    '&:hover': {\n                      transform: 'translateX(8px)',\n                      background: `${highlight.color}25`\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      color: highlight.color,\n                      mr: 2,\n                      display: 'flex',\n                      alignItems: 'center'\n                    },\n                    children: highlight.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      sx: {\n                        fontWeight: 600,\n                        mb: 0.5\n                      },\n                      children: highlight.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      sx: {\n                        fontSize: '0.875rem'\n                      },\n                      children: highlight.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n}\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "Container", "Typography", "Box", "Paper", "Grid", "Avatar", "Divider", "Chip", "CodeIcon", "SecurityIcon", "GroupsIcon", "SchoolIcon", "LocationOnIcon", "EmailIcon", "PhoneIcon", "AuroraBackground", "RotatingText", "jsxDEV", "_jsxDEV", "About", "highlights", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "color", "contactInfo", "text", "label", "max<PERSON><PERSON><PERSON>", "children", "colors", "opacity", "container", "spacing", "item", "xs", "md", "elevation", "className", "sx", "p", "height", "display", "flexDirection", "alignItems", "mb", "width", "mr", "background", "fontSize", "fontWeight", "variant", "words", "speed", "gap", "flexWrap", "size", "lineHeight", "map", "info", "index", "highlight", "borderRadius", "border", "transition", "transform", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/About.js"], "sourcesContent": ["import React from 'react';\r\nimport Container from '@mui/material/Container';\r\nimport Typography from '@mui/material/Typography';\r\nimport Box from '@mui/material/Box';\r\nimport Paper from '@mui/material/Paper';\r\nimport Grid from '@mui/material/Grid';\r\nimport Avatar from '@mui/material/Avatar';\r\nimport { Divider, Chip } from '@mui/material';\r\nimport CodeIcon from '@mui/icons-material/Code';\r\nimport SecurityIcon from '@mui/icons-material/Security';\r\nimport GroupsIcon from '@mui/icons-material/Groups';\r\nimport SchoolIcon from '@mui/icons-material/School';\r\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\r\nimport EmailIcon from '@mui/icons-material/Email';\r\nimport PhoneIcon from '@mui/icons-material/Phone';\r\nimport { AuroraBackground, RotatingText } from './effects';\r\n\r\nfunction About() {\r\n  const highlights = [\r\n    {\r\n      icon: <CodeIcon />,\r\n      title: \"AI/ML Expert\",\r\n      description: \"Advanced projects in machine learning and computer vision\",\r\n      color: \"#6366f1\"\r\n    },\r\n    {\r\n      icon: <SecurityIcon />,\r\n      title: \"Cybersecurity\",\r\n      description: \"CTF champion with penetration testing expertise\",\r\n      color: \"#ef4444\"\r\n    },\r\n    {\r\n      icon: <GroupsIcon />,\r\n      title: \"Leadership\",\r\n      description: \"Active in multiple tech clubs and organizations\",\r\n      color: \"#10b981\"\r\n    },\r\n    {\r\n      icon: <SchoolIcon />,\r\n      title: \"Published Author\",\r\n      description: \"Author of cybersecurity book on Amazon\",\r\n      color: \"#f59e0b\"\r\n    }\r\n  ];\r\n\r\n  const contactInfo = [\r\n    { icon: <LocationOnIcon />, text: \"Egattur Chennai, Tamil Nadu\", label: \"Location\" },\r\n    { icon: <EmailIcon />, text: \"<EMAIL>\", label: \"Email\" },\r\n    { icon: <PhoneIcon />, text: \"91+ 8939776542\", label: \"Phone\" }\r\n  ];\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\">\r\n      <AuroraBackground colors={['#007AFF', '#5AC8FA', '#FF9500']} opacity={0.08}>\r\n        <Grid container spacing={4}>\r\n        {/* Hero Section */}\r\n        <Grid item xs={12} md={8}>\r\n          <Paper\r\n            elevation={0}\r\n            className=\"glass-effect\"\r\n            sx={{\r\n              p: { xs: 3, md: 4 },\r\n              height: '100%',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n            }}\r\n          >\r\n            {/* Header with Avatar */}\r\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>\r\n              <Avatar\r\n                sx={{\r\n                  width: 80,\r\n                  height: 80,\r\n                  mr: 3,\r\n                  background: 'linear-gradient(135deg, #6366f1, #8b5cf6)',\r\n                  fontSize: '2rem',\r\n                  fontWeight: 'bold'\r\n                }}\r\n              >\r\n                JP\r\n              </Avatar>\r\n              <Box>\r\n                <Typography\r\n                  variant=\"h3\"\r\n                  className=\"gradient-text\"\r\n                  sx={{ mb: 1, fontWeight: 700 }}\r\n                >\r\n                  Joel Prince\r\n                </Typography>\r\n                <RotatingText\r\n                  words={[\r\n                    'Computer Science Student at VIT Chennai',\r\n                    'AI/ML Enthusiast & Researcher',\r\n                    'Cybersecurity Expert & CTF Champion',\r\n                    'Full-Stack Developer',\r\n                    'Published Author & Tech Leader'\r\n                  ]}\r\n                  variant=\"h6\"\r\n                  sx={{ mb: 1, color: 'text.secondary' }}\r\n                  speed={3000}\r\n                />\r\n                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\r\n                  <Chip\r\n                    label=\"HackClub VIT\"\r\n                    size=\"small\"\r\n                    sx={{ background: 'linear-gradient(45deg, #6366f1, #8b5cf6)', color: 'white' }}\r\n                  />\r\n                  <Chip\r\n                    label=\"Linux Club VIT\"\r\n                    size=\"small\"\r\n                    sx={{ background: 'linear-gradient(45deg, #06b6d4, #10b981)', color: 'white' }}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n            </Box>\r\n\r\n            {/* Bio */}\r\n            <Typography\r\n              variant=\"body1\"\r\n              sx={{\r\n                lineHeight: 1.8,\r\n                mb: 4,\r\n                fontSize: '1.1rem'\r\n              }}\r\n            >\r\n              I'm a passionate Computer Science student specializing in <strong>AI/ML</strong>, <strong>cybersecurity</strong>,\r\n              and <strong>full-stack development</strong>. My portfolio includes cutting-edge projects like MarketAnalyzerX\r\n              for stock market analysis, an Autonomous Tactical Navigation System with 94% threat detection accuracy,\r\n              and reinforcement learning models for DDoS defense.\r\n            </Typography>\r\n\r\n            <Typography\r\n              variant=\"body1\"\r\n              sx={{\r\n                lineHeight: 1.8,\r\n                mb: 4,\r\n                fontSize: '1.1rem'\r\n              }}\r\n            >\r\n              As a <strong>CTF champion</strong> (2nd place CryptoClash CTF 2025, 77th rank HackerEarth CTF Asia 2025)\r\n              and <strong>published author</strong> of \"From Code To Combat: The Silent War Where Algorithms Strike First\",\r\n              I bring both theoretical knowledge and practical experience to every project.\r\n            </Typography>\r\n          </Paper>\r\n        </Grid>\r\n\r\n        {/* Sidebar */}\r\n        <Grid item xs={12} md={4}>\r\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>\r\n            {/* Contact Info */}\r\n            <Paper className=\"glass-effect\" sx={{ p: 3 }}>\r\n              <Typography variant=\"h6\" className=\"gradient-text\" sx={{ mb: 3 }}>\r\n                Contact Information\r\n              </Typography>\r\n              {contactInfo.map((info, index) => (\r\n                <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\r\n                  <Box sx={{ color: 'primary.main', mr: 2 }}>\r\n                    {info.icon}\r\n                  </Box>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    {info.text}\r\n                  </Typography>\r\n                </Box>\r\n              ))}\r\n            </Paper>\r\n\r\n            {/* Highlights */}\r\n            <Paper className=\"glass-effect\" sx={{ p: 3 }}>\r\n              <Typography variant=\"h6\" className=\"gradient-text\" sx={{ mb: 3 }}>\r\n                Key Highlights\r\n              </Typography>\r\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n                {highlights.map((highlight, index) => (\r\n                  <Box\r\n                    key={index}\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      p: 2,\r\n                      borderRadius: 2,\r\n                      background: `${highlight.color}15`,\r\n                      border: `1px solid ${highlight.color}30`,\r\n                      transition: 'all 0.3s ease',\r\n                      '&:hover': {\r\n                        transform: 'translateX(8px)',\r\n                        background: `${highlight.color}25`,\r\n                      }\r\n                    }}\r\n                  >\r\n                    <Box\r\n                      sx={{\r\n                        color: highlight.color,\r\n                        mr: 2,\r\n                        display: 'flex',\r\n                        alignItems: 'center'\r\n                      }}\r\n                    >\r\n                      {highlight.icon}\r\n                    </Box>\r\n                    <Box>\r\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, mb: 0.5 }}>\r\n                        {highlight.title}\r\n                      </Typography>\r\n                      <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontSize: '0.875rem' }}>\r\n                        {highlight.description}\r\n                      </Typography>\r\n                    </Box>\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            </Paper>\r\n          </Box>\r\n        </Grid>\r\n      </Grid>\r\n      </AuroraBackground>\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default About; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,EAAEC,IAAI,QAAQ,eAAe;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,KAAKA,CAAA,EAAG;EACf,MAAMC,UAAU,GAAG,CACjB;IACEC,IAAI,eAAEH,OAAA,CAACV,QAAQ;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,2DAA2D;IACxEC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACT,YAAY;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,iDAAiD;IAC9DC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACR,UAAU;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,iDAAiD;IAC9DC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACP,UAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,wCAAwC;IACrDC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IAAER,IAAI,eAAEH,OAAA,CAACN,cAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEK,IAAI,EAAE,6BAA6B;IAAEC,KAAK,EAAE;EAAW,CAAC,EACpF;IAAEV,IAAI,eAAEH,OAAA,CAACL,SAAS;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEK,IAAI,EAAE,2BAA2B;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC1E;IAAEV,IAAI,eAAEH,OAAA,CAACJ,SAAS;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEK,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAQ,CAAC,CAChE;EAED,oBACEb,OAAA,CAAClB,SAAS;IAACgC,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtBf,OAAA,CAACH,gBAAgB;MAACmB,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAE;MAACC,OAAO,EAAE,IAAK;MAAAF,QAAA,eACzEf,OAAA,CAACd,IAAI;QAACgC,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAJ,QAAA,gBAE3Bf,OAAA,CAACd,IAAI;UAACkC,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eACvBf,OAAA,CAACf,KAAK;YACJsC,SAAS,EAAE,CAAE;YACbC,SAAS,EAAC,cAAc;YACxBC,EAAE,EAAE;cACFC,CAAC,EAAE;gBAAEL,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACnBK,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE;YACjB,CAAE;YAAAd,QAAA,gBAGFf,OAAA,CAAChB,GAAG;cAACyC,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAhB,QAAA,gBACxDf,OAAA,CAACb,MAAM;gBACLsC,EAAE,EAAE;kBACFO,KAAK,EAAE,EAAE;kBACTL,MAAM,EAAE,EAAE;kBACVM,EAAE,EAAE,CAAC;kBACLC,UAAU,EAAE,2CAA2C;kBACvDC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE;gBACd,CAAE;gBAAArB,QAAA,EACH;cAED;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTP,OAAA,CAAChB,GAAG;gBAAA+B,QAAA,gBACFf,OAAA,CAACjB,UAAU;kBACTsD,OAAO,EAAC,IAAI;kBACZb,SAAS,EAAC,eAAe;kBACzBC,EAAE,EAAE;oBAAEM,EAAE,EAAE,CAAC;oBAAEK,UAAU,EAAE;kBAAI,CAAE;kBAAArB,QAAA,EAChC;gBAED;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbP,OAAA,CAACF,YAAY;kBACXwC,KAAK,EAAE,CACL,yCAAyC,EACzC,+BAA+B,EAC/B,qCAAqC,EACrC,sBAAsB,EACtB,gCAAgC,CAChC;kBACFD,OAAO,EAAC,IAAI;kBACZZ,EAAE,EAAE;oBAAEM,EAAE,EAAE,CAAC;oBAAErB,KAAK,EAAE;kBAAiB,CAAE;kBACvC6B,KAAK,EAAE;gBAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACFP,OAAA,CAAChB,GAAG;kBAACyC,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAEY,GAAG,EAAE,CAAC;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAA1B,QAAA,gBACrDf,OAAA,CAACX,IAAI;oBACHwB,KAAK,EAAC,cAAc;oBACpB6B,IAAI,EAAC,OAAO;oBACZjB,EAAE,EAAE;sBAAES,UAAU,EAAE,0CAA0C;sBAAExB,KAAK,EAAE;oBAAQ;kBAAE;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACFP,OAAA,CAACX,IAAI;oBACHwB,KAAK,EAAC,gBAAgB;oBACtB6B,IAAI,EAAC,OAAO;oBACZjB,EAAE,EAAE;sBAAES,UAAU,EAAE,0CAA0C;sBAAExB,KAAK,EAAE;oBAAQ;kBAAE;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNP,OAAA,CAACjB,UAAU;cACTsD,OAAO,EAAC,OAAO;cACfZ,EAAE,EAAE;gBACFkB,UAAU,EAAE,GAAG;gBACfZ,EAAE,EAAE,CAAC;gBACLI,QAAQ,EAAE;cACZ,CAAE;cAAApB,QAAA,GACH,4DAC2D,eAAAf,OAAA;gBAAAe,QAAA,EAAQ;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,MAAE,eAAAP,OAAA;gBAAAe,QAAA,EAAQ;cAAa;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,UAC5G,eAAAP,OAAA;gBAAAe,QAAA,EAAQ;cAAsB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kOAG7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbP,OAAA,CAACjB,UAAU;cACTsD,OAAO,EAAC,OAAO;cACfZ,EAAE,EAAE;gBACFkB,UAAU,EAAE,GAAG;gBACfZ,EAAE,EAAE,CAAC;gBACLI,QAAQ,EAAE;cACZ,CAAE;cAAApB,QAAA,GACH,OACM,eAAAf,OAAA;gBAAAe,QAAA,EAAQ;cAAY;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,+EAC9B,eAAAP,OAAA;gBAAAe,QAAA,EAAQ;cAAgB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,4JAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPP,OAAA,CAACd,IAAI;UAACkC,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eACvBf,OAAA,CAAChB,GAAG;YAACyC,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAEW,GAAG,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAE5Df,OAAA,CAACf,KAAK;cAACuC,SAAS,EAAC,cAAc;cAACC,EAAE,EAAE;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAAAX,QAAA,gBAC3Cf,OAAA,CAACjB,UAAU;gBAACsD,OAAO,EAAC,IAAI;gBAACb,SAAS,EAAC,eAAe;gBAACC,EAAE,EAAE;kBAAEM,EAAE,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EAAC;cAElE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZI,WAAW,CAACiC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3B9C,OAAA,CAAChB,GAAG;gBAAayC,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,gBACpEf,OAAA,CAAChB,GAAG;kBAACyC,EAAE,EAAE;oBAAEf,KAAK,EAAE,cAAc;oBAAEuB,EAAE,EAAE;kBAAE,CAAE;kBAAAlB,QAAA,EACvC8B,IAAI,CAAC1C;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACNP,OAAA,CAACjB,UAAU;kBAACsD,OAAO,EAAC,OAAO;kBAAC3B,KAAK,EAAC,gBAAgB;kBAAAK,QAAA,EAC/C8B,IAAI,CAACjC;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GANLuC,KAAK;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGRP,OAAA,CAACf,KAAK;cAACuC,SAAS,EAAC,cAAc;cAACC,EAAE,EAAE;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAAAX,QAAA,gBAC3Cf,OAAA,CAACjB,UAAU;gBAACsD,OAAO,EAAC,IAAI;gBAACb,SAAS,EAAC,eAAe;gBAACC,EAAE,EAAE;kBAAEM,EAAE,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EAAC;cAElE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbP,OAAA,CAAChB,GAAG;gBAACyC,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEC,aAAa,EAAE,QAAQ;kBAAEW,GAAG,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EAC3Db,UAAU,CAAC0C,GAAG,CAAC,CAACG,SAAS,EAAED,KAAK,kBAC/B9C,OAAA,CAAChB,GAAG;kBAEFyC,EAAE,EAAE;oBACFG,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBJ,CAAC,EAAE,CAAC;oBACJsB,YAAY,EAAE,CAAC;oBACfd,UAAU,EAAE,GAAGa,SAAS,CAACrC,KAAK,IAAI;oBAClCuC,MAAM,EAAE,aAAaF,SAAS,CAACrC,KAAK,IAAI;oBACxCwC,UAAU,EAAE,eAAe;oBAC3B,SAAS,EAAE;sBACTC,SAAS,EAAE,iBAAiB;sBAC5BjB,UAAU,EAAE,GAAGa,SAAS,CAACrC,KAAK;oBAChC;kBACF,CAAE;kBAAAK,QAAA,gBAEFf,OAAA,CAAChB,GAAG;oBACFyC,EAAE,EAAE;sBACFf,KAAK,EAAEqC,SAAS,CAACrC,KAAK;sBACtBuB,EAAE,EAAE,CAAC;sBACLL,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE;oBACd,CAAE;oBAAAf,QAAA,EAEDgC,SAAS,CAAC5C;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACNP,OAAA,CAAChB,GAAG;oBAAA+B,QAAA,gBACFf,OAAA,CAACjB,UAAU;sBAACsD,OAAO,EAAC,WAAW;sBAACZ,EAAE,EAAE;wBAAEW,UAAU,EAAE,GAAG;wBAAEL,EAAE,EAAE;sBAAI,CAAE;sBAAAhB,QAAA,EAC9DgC,SAAS,CAACvC;oBAAK;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACbP,OAAA,CAACjB,UAAU;sBAACsD,OAAO,EAAC,OAAO;sBAAC3B,KAAK,EAAC,gBAAgB;sBAACe,EAAE,EAAE;wBAAEU,QAAQ,EAAE;sBAAW,CAAE;sBAAApB,QAAA,EAC7EgC,SAAS,CAACtC;oBAAW;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA,GAhCDuC,KAAK;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEhB;AAAC6C,EAAA,GAxMQnD,KAAK;AA0Md,eAAeA,KAAK;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}