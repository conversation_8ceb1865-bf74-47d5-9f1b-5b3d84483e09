import React, { useState, useEffect } from 'react';
import { Box, Container, Fade } from '@mui/material';

const EnhancedSection = ({ 
  children, 
  id, 
  backgroundEffect = null, 
  glowColor = '#007AFF',
  minHeight = 'auto'
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    const element = document.getElementById(id);
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [id]);

  const sectionContent = (
    <Box
      id={id}
      component="section"
      sx={{
        minHeight: { xs: 'auto', md: minHeight },
        display: 'flex',
        alignItems: 'center',
        py: { xs: 8, sm: 12, md: 16 },
        px: { xs: 2, sm: 4, md: 6 },
        position: 'relative',
        scrollMarginTop: { xs: '56px', sm: '64px' },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: '50%',
          transform: 'translateX(-50%)',
          width: '90%',
          height: '1px',
          background: `linear-gradient(90deg, transparent 0%, ${glowColor}40 50%, transparent 100%)`,
          opacity: isVisible ? 1 : 0,
          transition: 'opacity 1s ease-in-out',
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: '50%',
          transform: 'translateX(-50%)',
          width: '90%',
          height: '1px',
          background: `linear-gradient(90deg, transparent 0%, ${glowColor}20 50%, transparent 100%)`,
          opacity: isVisible ? 1 : 0,
          transition: 'opacity 1s ease-in-out 0.5s',
        }
      }}
    >
      <Container
        maxWidth="lg"
        sx={{
          width: '100%',
          mx: 'auto',
          position: 'relative',
          zIndex: 2,
        }}
      >
        <Fade in={isVisible} timeout={1000}>
          <Box
            sx={{
              transform: isVisible ? 'translateY(0)' : 'translateY(40px)',
              transition: 'transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            }}
          >
            {children}
          </Box>
        </Fade>
      </Container>
    </Box>
  );

  if (backgroundEffect) {
    return React.cloneElement(backgroundEffect, {
      children: sectionContent
    });
  }

  return sectionContent;
};

export default EnhancedSection;
