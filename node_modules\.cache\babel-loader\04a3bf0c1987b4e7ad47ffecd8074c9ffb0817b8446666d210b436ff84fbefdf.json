{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\GridDistortion.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GridDistortion = ({\n  children,\n  gridSize = 40,\n  distortionStrength = 0.2,\n  color = '#9c27b0',\n  opacity = 0.1\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    let animationId;\n    let time = 0;\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n    const drawGrid = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      const cols = Math.ceil(canvas.width / gridSize);\n      const rows = Math.ceil(canvas.height / gridSize);\n      ctx.strokeStyle = `${color}40`;\n      ctx.lineWidth = 1;\n\n      // Draw vertical lines\n      for (let i = 0; i <= cols; i++) {\n        ctx.beginPath();\n        for (let j = 0; j <= rows; j++) {\n          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;\n          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;\n          if (j === 0) {\n            ctx.moveTo(x, y);\n          } else {\n            ctx.lineTo(x, y);\n          }\n        }\n        ctx.stroke();\n      }\n\n      // Draw horizontal lines\n      for (let j = 0; j <= rows; j++) {\n        ctx.beginPath();\n        for (let i = 0; i <= cols; i++) {\n          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;\n          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;\n          if (i === 0) {\n            ctx.moveTo(x, y);\n          } else {\n            ctx.lineTo(x, y);\n          }\n        }\n        ctx.stroke();\n      }\n\n      // Add intersection points with glow\n      ctx.fillStyle = `${color}80`;\n      ctx.shadowColor = color;\n      ctx.shadowBlur = 5;\n      for (let i = 0; i <= cols; i += 2) {\n        for (let j = 0; j <= rows; j += 2) {\n          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;\n          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;\n          const pulseSize = 2 + Math.sin(time * 0.02 + i + j) * 1;\n          ctx.beginPath();\n          ctx.arc(x, y, pulseSize, 0, Math.PI * 2);\n          ctx.fill();\n        }\n      }\n      ctx.shadowBlur = 0;\n    };\n    const animate = () => {\n      time += 1;\n      drawGrid();\n      animationId = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [gridSize, distortionStrength, color]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: 0,\n        pointerEvents: 'none',\n        opacity: 0.3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(GridDistortion, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = GridDistortion;\nexport default GridDistortion;\nvar _c;\n$RefreshReg$(_c, \"GridDistortion\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "jsxDEV", "_jsxDEV", "GridDistortion", "children", "gridSize", "distortionStrength", "color", "opacity", "_s", "canvasRef", "canvas", "current", "ctx", "getContext", "animationId", "time", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "drawGrid", "clearRect", "cols", "Math", "ceil", "rows", "strokeStyle", "lineWidth", "i", "beginPath", "j", "x", "sin", "y", "cos", "moveTo", "lineTo", "stroke", "fillStyle", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "pulseSize", "arc", "PI", "fill", "animate", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "sx", "position", "overflow", "ref", "style", "top", "left", "zIndex", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/GridDistortion.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\n\nconst GridDistortion = ({ children, gridSize = 40, distortionStrength = 0.2, color = '#9c27b0', opacity = 0.1 }) => {\n  const canvasRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationId;\n    let time = 0;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    const drawGrid = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      const cols = Math.ceil(canvas.width / gridSize);\n      const rows = Math.ceil(canvas.height / gridSize);\n\n      ctx.strokeStyle = `${color}40`;\n      ctx.lineWidth = 1;\n\n      // Draw vertical lines\n      for (let i = 0; i <= cols; i++) {\n        ctx.beginPath();\n        for (let j = 0; j <= rows; j++) {\n          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;\n          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;\n          \n          if (j === 0) {\n            ctx.moveTo(x, y);\n          } else {\n            ctx.lineTo(x, y);\n          }\n        }\n        ctx.stroke();\n      }\n\n      // Draw horizontal lines\n      for (let j = 0; j <= rows; j++) {\n        ctx.beginPath();\n        for (let i = 0; i <= cols; i++) {\n          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;\n          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;\n          \n          if (i === 0) {\n            ctx.moveTo(x, y);\n          } else {\n            ctx.lineTo(x, y);\n          }\n        }\n        ctx.stroke();\n      }\n\n      // Add intersection points with glow\n      ctx.fillStyle = `${color}80`;\n      ctx.shadowColor = color;\n      ctx.shadowBlur = 5;\n      \n      for (let i = 0; i <= cols; i += 2) {\n        for (let j = 0; j <= rows; j += 2) {\n          const x = i * gridSize + Math.sin(time * 0.01 + i * 0.1 + j * 0.05) * distortionStrength * 10;\n          const y = j * gridSize + Math.cos(time * 0.01 + i * 0.05 + j * 0.1) * distortionStrength * 10;\n          \n          const pulseSize = 2 + Math.sin(time * 0.02 + i + j) * 1;\n          ctx.beginPath();\n          ctx.arc(x, y, pulseSize, 0, Math.PI * 2);\n          ctx.fill();\n        }\n      }\n      \n      ctx.shadowBlur = 0;\n    };\n\n    const animate = () => {\n      time += 1;\n      drawGrid();\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [gridSize, distortionStrength, color]);\n\n  return (\n    <Box sx={{ position: 'relative', overflow: 'hidden' }}>\n      <canvas\n        ref={canvasRef}\n        style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          zIndex: 0,\n          pointerEvents: 'none',\n          opacity: 0.3,\n        }}\n      />\n      <Box sx={{ position: 'relative', zIndex: 1 }}>\n        {children}\n      </Box>\n    </Box>\n  );\n};\n\nexport default GridDistortion;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ,GAAG,EAAE;EAAEC,kBAAkB,GAAG,GAAG;EAAEC,KAAK,GAAG,SAAS;EAAEC,OAAO,GAAG;AAAI,CAAC,KAAK;EAAAC,EAAA;EAClH,MAAMC,SAAS,GAAGX,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,MAAMa,MAAM,GAAGD,SAAS,CAACE,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAIC,WAAW;IACf,IAAIC,IAAI,GAAG,CAAC;IAEZ,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBN,MAAM,CAACO,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCT,MAAM,CAACU,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAE/C,MAAMO,QAAQ,GAAGA,CAAA,KAAM;MACrBX,GAAG,CAACY,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEd,MAAM,CAACO,KAAK,EAAEP,MAAM,CAACU,MAAM,CAAC;MAEhD,MAAMK,IAAI,GAAGC,IAAI,CAACC,IAAI,CAACjB,MAAM,CAACO,KAAK,GAAGb,QAAQ,CAAC;MAC/C,MAAMwB,IAAI,GAAGF,IAAI,CAACC,IAAI,CAACjB,MAAM,CAACU,MAAM,GAAGhB,QAAQ,CAAC;MAEhDQ,GAAG,CAACiB,WAAW,GAAG,GAAGvB,KAAK,IAAI;MAC9BM,GAAG,CAACkB,SAAS,GAAG,CAAC;;MAEjB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIN,IAAI,EAAEM,CAAC,EAAE,EAAE;QAC9BnB,GAAG,CAACoB,SAAS,CAAC,CAAC;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,IAAI,EAAEK,CAAC,EAAE,EAAE;UAC9B,MAAMC,CAAC,GAAGH,CAAC,GAAG3B,QAAQ,GAAGsB,IAAI,CAACS,GAAG,CAACpB,IAAI,GAAG,IAAI,GAAGgB,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,IAAI,CAAC,GAAG5B,kBAAkB,GAAG,EAAE;UAC7F,MAAM+B,CAAC,GAAGH,CAAC,GAAG7B,QAAQ,GAAGsB,IAAI,CAACW,GAAG,CAACtB,IAAI,GAAG,IAAI,GAAGgB,CAAC,GAAG,IAAI,GAAGE,CAAC,GAAG,GAAG,CAAC,GAAG5B,kBAAkB,GAAG,EAAE;UAE7F,IAAI4B,CAAC,KAAK,CAAC,EAAE;YACXrB,GAAG,CAAC0B,MAAM,CAACJ,CAAC,EAAEE,CAAC,CAAC;UAClB,CAAC,MAAM;YACLxB,GAAG,CAAC2B,MAAM,CAACL,CAAC,EAAEE,CAAC,CAAC;UAClB;QACF;QACAxB,GAAG,CAAC4B,MAAM,CAAC,CAAC;MACd;;MAEA;MACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,IAAI,EAAEK,CAAC,EAAE,EAAE;QAC9BrB,GAAG,CAACoB,SAAS,CAAC,CAAC;QACf,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIN,IAAI,EAAEM,CAAC,EAAE,EAAE;UAC9B,MAAMG,CAAC,GAAGH,CAAC,GAAG3B,QAAQ,GAAGsB,IAAI,CAACS,GAAG,CAACpB,IAAI,GAAG,IAAI,GAAGgB,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,IAAI,CAAC,GAAG5B,kBAAkB,GAAG,EAAE;UAC7F,MAAM+B,CAAC,GAAGH,CAAC,GAAG7B,QAAQ,GAAGsB,IAAI,CAACW,GAAG,CAACtB,IAAI,GAAG,IAAI,GAAGgB,CAAC,GAAG,IAAI,GAAGE,CAAC,GAAG,GAAG,CAAC,GAAG5B,kBAAkB,GAAG,EAAE;UAE7F,IAAI0B,CAAC,KAAK,CAAC,EAAE;YACXnB,GAAG,CAAC0B,MAAM,CAACJ,CAAC,EAAEE,CAAC,CAAC;UAClB,CAAC,MAAM;YACLxB,GAAG,CAAC2B,MAAM,CAACL,CAAC,EAAEE,CAAC,CAAC;UAClB;QACF;QACAxB,GAAG,CAAC4B,MAAM,CAAC,CAAC;MACd;;MAEA;MACA5B,GAAG,CAAC6B,SAAS,GAAG,GAAGnC,KAAK,IAAI;MAC5BM,GAAG,CAAC8B,WAAW,GAAGpC,KAAK;MACvBM,GAAG,CAAC+B,UAAU,GAAG,CAAC;MAElB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIN,IAAI,EAAEM,CAAC,IAAI,CAAC,EAAE;QACjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIL,IAAI,EAAEK,CAAC,IAAI,CAAC,EAAE;UACjC,MAAMC,CAAC,GAAGH,CAAC,GAAG3B,QAAQ,GAAGsB,IAAI,CAACS,GAAG,CAACpB,IAAI,GAAG,IAAI,GAAGgB,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,IAAI,CAAC,GAAG5B,kBAAkB,GAAG,EAAE;UAC7F,MAAM+B,CAAC,GAAGH,CAAC,GAAG7B,QAAQ,GAAGsB,IAAI,CAACW,GAAG,CAACtB,IAAI,GAAG,IAAI,GAAGgB,CAAC,GAAG,IAAI,GAAGE,CAAC,GAAG,GAAG,CAAC,GAAG5B,kBAAkB,GAAG,EAAE;UAE7F,MAAMuC,SAAS,GAAG,CAAC,GAAGlB,IAAI,CAACS,GAAG,CAACpB,IAAI,GAAG,IAAI,GAAGgB,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC;UACvDrB,GAAG,CAACoB,SAAS,CAAC,CAAC;UACfpB,GAAG,CAACiC,GAAG,CAACX,CAAC,EAAEE,CAAC,EAAEQ,SAAS,EAAE,CAAC,EAAElB,IAAI,CAACoB,EAAE,GAAG,CAAC,CAAC;UACxClC,GAAG,CAACmC,IAAI,CAAC,CAAC;QACZ;MACF;MAEAnC,GAAG,CAAC+B,UAAU,GAAG,CAAC;IACpB,CAAC;IAED,MAAMK,OAAO,GAAGA,CAAA,KAAM;MACpBjC,IAAI,IAAI,CAAC;MACTQ,QAAQ,CAAC,CAAC;MACVT,WAAW,GAAGmC,qBAAqB,CAACD,OAAO,CAAC;IAC9C,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACX9B,MAAM,CAACgC,mBAAmB,CAAC,QAAQ,EAAElC,YAAY,CAAC;MAClD,IAAIF,WAAW,EAAE;QACfqC,oBAAoB,CAACrC,WAAW,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAACV,QAAQ,EAAEC,kBAAkB,EAAEC,KAAK,CAAC,CAAC;EAEzC,oBACEL,OAAA,CAACF,GAAG;IAACqD,EAAE,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAnD,QAAA,gBACpDF,OAAA;MACEsD,GAAG,EAAE9C,SAAU;MACf+C,KAAK,EAAE;QACLH,QAAQ,EAAE,UAAU;QACpBI,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPzC,KAAK,EAAE,MAAM;QACbG,MAAM,EAAE,MAAM;QACduC,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE,MAAM;QACrBrD,OAAO,EAAE;MACX;IAAE;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF/D,OAAA,CAACF,GAAG;MAACqD,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEM,MAAM,EAAE;MAAE,CAAE;MAAAxD,QAAA,EAC1CA;IAAQ;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxD,EAAA,CApHIN,cAAc;AAAA+D,EAAA,GAAd/D,cAAc;AAsHpB,eAAeA,cAAc;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}