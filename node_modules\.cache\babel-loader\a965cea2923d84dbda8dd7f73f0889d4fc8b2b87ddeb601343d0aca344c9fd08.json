{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\RotatingText.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Typography, Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RotatingText = ({\n  words = [],\n  variant = 'h6',\n  className = '',\n  sx = {},\n  speed = 2000,\n  prefix = '',\n  suffix = '',\n  ...props\n}) => {\n  _s();\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAnimating, setIsAnimating] = useState(false);\n  useEffect(() => {\n    if (words.length <= 1) return;\n    const interval = setInterval(() => {\n      setIsAnimating(true);\n      setTimeout(() => {\n        setCurrentIndex(prevIndex => (prevIndex + 1) % words.length);\n        setIsAnimating(false);\n      }, 200); // Half of the animation duration\n    }, speed);\n    return () => clearInterval(interval);\n  }, [words, speed]);\n  if (!words.length) return null;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'inline-flex',\n      alignItems: 'center',\n      overflow: 'hidden'\n    },\n    children: [prefix && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: variant,\n      className: className,\n      sx: sx,\n      ...props,\n      children: prefix\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        display: 'inline-block',\n        minWidth: '120px',\n        // Adjust based on your content\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: variant,\n        className: className,\n        sx: {\n          background: 'linear-gradient(45deg, #007AFF, #5AC8FA, #FF9500)',\n          backgroundSize: '200% 200%',\n          animation: 'gradient-shift 4s ease infinite',\n          WebkitBackgroundClip: 'text',\n          WebkitTextFillColor: 'transparent',\n          backgroundClip: 'text',\n          transform: isAnimating ? 'translateY(-10px)' : 'translateY(0px)',\n          opacity: isAnimating ? 0.3 : 1,\n          transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n          '@keyframes gradient-shift': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          ...sx\n        },\n        ...props,\n        children: words[currentIndex]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), suffix && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: variant,\n      className: className,\n      sx: sx,\n      ...props,\n      children: suffix\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(RotatingText, \"OvY+qMktgEMIZr3XjyGWJ+Bj9IQ=\");\n_c = RotatingText;\nexport default RotatingText;\nvar _c;\n$RefreshReg$(_c, \"RotatingText\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Typography", "Box", "jsxDEV", "_jsxDEV", "RotatingText", "words", "variant", "className", "sx", "speed", "prefix", "suffix", "props", "_s", "currentIndex", "setCurrentIndex", "isAnimating", "setIsAnimating", "length", "interval", "setInterval", "setTimeout", "prevIndex", "clearInterval", "display", "alignItems", "overflow", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "min<PERSON><PERSON><PERSON>", "textAlign", "background", "backgroundSize", "animation", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "transform", "opacity", "transition", "backgroundPosition", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/RotatingText.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Typography, Box } from '@mui/material';\n\nconst RotatingText = ({ \n  words = [], \n  variant = 'h6', \n  className = '', \n  sx = {}, \n  speed = 2000,\n  prefix = '',\n  suffix = '',\n  ...props \n}) => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  useEffect(() => {\n    if (words.length <= 1) return;\n\n    const interval = setInterval(() => {\n      setIsAnimating(true);\n      \n      setTimeout(() => {\n        setCurrentIndex((prevIndex) => (prevIndex + 1) % words.length);\n        setIsAnimating(false);\n      }, 200); // Half of the animation duration\n      \n    }, speed);\n\n    return () => clearInterval(interval);\n  }, [words, speed]);\n\n  if (!words.length) return null;\n\n  return (\n    <Box sx={{ display: 'inline-flex', alignItems: 'center', overflow: 'hidden' }}>\n      {prefix && (\n        <Typography variant={variant} className={className} sx={sx} {...props}>\n          {prefix}\n        </Typography>\n      )}\n      \n      <Box\n        sx={{\n          position: 'relative',\n          display: 'inline-block',\n          minWidth: '120px', // Adjust based on your content\n          textAlign: 'center',\n        }}\n      >\n        <Typography\n          variant={variant}\n          className={className}\n          sx={{\n            background: 'linear-gradient(45deg, #007AFF, #5AC8FA, #FF9500)',\n            backgroundSize: '200% 200%',\n            animation: 'gradient-shift 4s ease infinite',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            backgroundClip: 'text',\n            transform: isAnimating ? 'translateY(-10px)' : 'translateY(0px)',\n            opacity: isAnimating ? 0.3 : 1,\n            transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n            '@keyframes gradient-shift': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' }\n            },\n            ...sx\n          }}\n          {...props}\n        >\n          {words[currentIndex]}\n        </Typography>\n      </Box>\n      \n      {suffix && (\n        <Typography variant={variant} className={className} sx={sx} {...props}>\n          {suffix}\n        </Typography>\n      )}\n    </Box>\n  );\n};\n\nexport default RotatingText;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,KAAK,GAAG,EAAE;EACVC,OAAO,GAAG,IAAI;EACdC,SAAS,GAAG,EAAE;EACdC,EAAE,GAAG,CAAC,CAAC;EACPC,KAAK,GAAG,IAAI;EACZC,MAAM,GAAG,EAAE;EACXC,MAAM,GAAG,EAAE;EACX,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACd,IAAIM,KAAK,CAACa,MAAM,IAAI,CAAC,EAAE;IAEvB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCH,cAAc,CAAC,IAAI,CAAC;MAEpBI,UAAU,CAAC,MAAM;QACfN,eAAe,CAAEO,SAAS,IAAK,CAACA,SAAS,GAAG,CAAC,IAAIjB,KAAK,CAACa,MAAM,CAAC;QAC9DD,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAEX,CAAC,EAAER,KAAK,CAAC;IAET,OAAO,MAAMc,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACd,KAAK,EAAEI,KAAK,CAAC,CAAC;EAElB,IAAI,CAACJ,KAAK,CAACa,MAAM,EAAE,OAAO,IAAI;EAE9B,oBACEf,OAAA,CAACF,GAAG;IAACO,EAAE,EAAE;MAAEgB,OAAO,EAAE,aAAa;MAAEC,UAAU,EAAE,QAAQ;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAC,QAAA,GAC3EjB,MAAM,iBACLP,OAAA,CAACH,UAAU;MAACM,OAAO,EAAEA,OAAQ;MAACC,SAAS,EAAEA,SAAU;MAACC,EAAE,EAAEA,EAAG;MAAA,GAAKI,KAAK;MAAAe,QAAA,EAClEjB;IAAM;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACb,eAED5B,OAAA,CAACF,GAAG;MACFO,EAAE,EAAE;QACFwB,QAAQ,EAAE,UAAU;QACpBR,OAAO,EAAE,cAAc;QACvBS,QAAQ,EAAE,OAAO;QAAE;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,eAEFxB,OAAA,CAACH,UAAU;QACTM,OAAO,EAAEA,OAAQ;QACjBC,SAAS,EAAEA,SAAU;QACrBC,EAAE,EAAE;UACF2B,UAAU,EAAE,mDAAmD;UAC/DC,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE,iCAAiC;UAC5CC,oBAAoB,EAAE,MAAM;UAC5BC,mBAAmB,EAAE,aAAa;UAClCC,cAAc,EAAE,MAAM;UACtBC,SAAS,EAAEzB,WAAW,GAAG,mBAAmB,GAAG,iBAAiB;UAChE0B,OAAO,EAAE1B,WAAW,GAAG,GAAG,GAAG,CAAC;UAC9B2B,UAAU,EAAE,+CAA+C;UAC3D,2BAA2B,EAAE;YAC3B,IAAI,EAAE;cAAEC,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,GAAGpC;QACL,CAAE;QAAA,GACEI,KAAK;QAAAe,QAAA,EAERtB,KAAK,CAACS,YAAY;MAAC;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELpB,MAAM,iBACLR,OAAA,CAACH,UAAU;MAACM,OAAO,EAAEA,OAAQ;MAACC,SAAS,EAAEA,SAAU;MAACC,EAAE,EAAEA,EAAG;MAAA,GAAKI,KAAK;MAAAe,QAAA,EAClEhB;IAAM;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClB,EAAA,CAhFIT,YAAY;AAAAyC,EAAA,GAAZzC,YAAY;AAkFlB,eAAeA,YAAY;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}