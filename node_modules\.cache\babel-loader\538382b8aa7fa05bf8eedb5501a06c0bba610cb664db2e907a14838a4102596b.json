{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\EnhancedSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Container, Fade } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedSection = ({\n  children,\n  id,\n  backgroundEffect = null,\n  glowColor = '#007AFF',\n  minHeight = 'auto'\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(false);\n  useEffect(() => {\n    const observer = new IntersectionObserver(([entry]) => {\n      if (entry.isIntersecting) {\n        setIsVisible(true);\n      }\n    }, {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    });\n    const element = document.getElementById(id);\n    if (element) {\n      observer.observe(element);\n    }\n    return () => {\n      if (element) {\n        observer.unobserve(element);\n      }\n    };\n  }, [id]);\n  const sectionContent = /*#__PURE__*/_jsxDEV(Box, {\n    id: id,\n    component: \"section\",\n    sx: {\n      minHeight: {\n        xs: 'auto',\n        md: minHeight\n      },\n      display: 'flex',\n      alignItems: 'center',\n      py: {\n        xs: 8,\n        sm: 12,\n        md: 16\n      },\n      px: {\n        xs: 2,\n        sm: 4,\n        md: 6\n      },\n      position: 'relative',\n      scrollMarginTop: {\n        xs: '56px',\n        sm: '64px'\n      },\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: '50%',\n        transform: 'translateX(-50%)',\n        width: '90%',\n        height: '1px',\n        background: `linear-gradient(90deg, transparent 0%, ${glowColor}40 50%, transparent 100%)`,\n        opacity: isVisible ? 1 : 0,\n        transition: 'opacity 1s ease-in-out'\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        bottom: 0,\n        left: '50%',\n        transform: 'translateX(-50%)',\n        width: '90%',\n        height: '1px',\n        background: `linear-gradient(90deg, transparent 0%, ${glowColor}20 50%, transparent 100%)`,\n        opacity: isVisible ? 1 : 0,\n        transition: 'opacity 1s ease-in-out 0.5s'\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        width: '100%',\n        mx: 'auto',\n        position: 'relative',\n        zIndex: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Fade, {\n        in: isVisible,\n        timeout: 1000,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            transform: isVisible ? 'translateY(0)' : 'translateY(40px)',\n            transition: 'transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)'\n          },\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n  if (backgroundEffect) {\n    return /*#__PURE__*/React.cloneElement(backgroundEffect, {\n      children: sectionContent\n    });\n  }\n  return sectionContent;\n};\n_s(EnhancedSection, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c = EnhancedSection;\nexport default EnhancedSection;\nvar _c;\n$RefreshReg$(_c, \"EnhancedSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Container", "Fade", "jsxDEV", "_jsxDEV", "EnhancedSection", "children", "id", "backgroundEffect", "glowColor", "minHeight", "_s", "isVisible", "setIsVisible", "observer", "IntersectionObserver", "entry", "isIntersecting", "threshold", "rootMargin", "element", "document", "getElementById", "observe", "unobserve", "sectionContent", "component", "sx", "xs", "md", "display", "alignItems", "py", "sm", "px", "position", "scrollMarginTop", "content", "top", "left", "transform", "width", "height", "background", "opacity", "transition", "bottom", "max<PERSON><PERSON><PERSON>", "mx", "zIndex", "in", "timeout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cloneElement", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/EnhancedSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Container, Fade } from '@mui/material';\n\nconst EnhancedSection = ({ \n  children, \n  id, \n  backgroundEffect = null, \n  glowColor = '#007AFF',\n  minHeight = 'auto'\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      {\n        threshold: 0.1,\n        rootMargin: '0px 0px -50px 0px'\n      }\n    );\n\n    const element = document.getElementById(id);\n    if (element) {\n      observer.observe(element);\n    }\n\n    return () => {\n      if (element) {\n        observer.unobserve(element);\n      }\n    };\n  }, [id]);\n\n  const sectionContent = (\n    <Box\n      id={id}\n      component=\"section\"\n      sx={{\n        minHeight: { xs: 'auto', md: minHeight },\n        display: 'flex',\n        alignItems: 'center',\n        py: { xs: 8, sm: 12, md: 16 },\n        px: { xs: 2, sm: 4, md: 6 },\n        position: 'relative',\n        scrollMarginTop: { xs: '56px', sm: '64px' },\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: '50%',\n          transform: 'translateX(-50%)',\n          width: '90%',\n          height: '1px',\n          background: `linear-gradient(90deg, transparent 0%, ${glowColor}40 50%, transparent 100%)`,\n          opacity: isVisible ? 1 : 0,\n          transition: 'opacity 1s ease-in-out',\n        },\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          bottom: 0,\n          left: '50%',\n          transform: 'translateX(-50%)',\n          width: '90%',\n          height: '1px',\n          background: `linear-gradient(90deg, transparent 0%, ${glowColor}20 50%, transparent 100%)`,\n          opacity: isVisible ? 1 : 0,\n          transition: 'opacity 1s ease-in-out 0.5s',\n        }\n      }}\n    >\n      <Container\n        maxWidth=\"lg\"\n        sx={{\n          width: '100%',\n          mx: 'auto',\n          position: 'relative',\n          zIndex: 2,\n        }}\n      >\n        <Fade in={isVisible} timeout={1000}>\n          <Box\n            sx={{\n              transform: isVisible ? 'translateY(0)' : 'translateY(40px)',\n              transition: 'transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n            }}\n          >\n            {children}\n          </Box>\n        </Fade>\n      </Container>\n    </Box>\n  );\n\n  if (backgroundEffect) {\n    return React.cloneElement(backgroundEffect, {\n      children: sectionContent\n    });\n  }\n\n  return sectionContent;\n};\n\nexport default EnhancedSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,SAAS,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,eAAe,GAAGA,CAAC;EACvBC,QAAQ;EACRC,EAAE;EACFC,gBAAgB,GAAG,IAAI;EACvBC,SAAS,GAAG,SAAS;EACrBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,MAAMe,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACX,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxBJ,YAAY,CAAC,IAAI,CAAC;MACpB;IACF,CAAC,EACD;MACEK,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE;IACd,CACF,CAAC;IAED,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACf,EAAE,CAAC;IAC3C,IAAIa,OAAO,EAAE;MACXN,QAAQ,CAACS,OAAO,CAACH,OAAO,CAAC;IAC3B;IAEA,OAAO,MAAM;MACX,IAAIA,OAAO,EAAE;QACXN,QAAQ,CAACU,SAAS,CAACJ,OAAO,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACb,EAAE,CAAC,CAAC;EAER,MAAMkB,cAAc,gBAClBrB,OAAA,CAACJ,GAAG;IACFO,EAAE,EAAEA,EAAG;IACPmB,SAAS,EAAC,SAAS;IACnBC,EAAE,EAAE;MACFjB,SAAS,EAAE;QAAEkB,EAAE,EAAE,MAAM;QAAEC,EAAE,EAAEnB;MAAU,CAAC;MACxCoB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE;QAAEJ,EAAE,EAAE,CAAC;QAAEK,EAAE,EAAE,EAAE;QAAEJ,EAAE,EAAE;MAAG,CAAC;MAC7BK,EAAE,EAAE;QAAEN,EAAE,EAAE,CAAC;QAAEK,EAAE,EAAE,CAAC;QAAEJ,EAAE,EAAE;MAAE,CAAC;MAC3BM,QAAQ,EAAE,UAAU;MACpBC,eAAe,EAAE;QAAER,EAAE,EAAE,MAAM;QAAEK,EAAE,EAAE;MAAO,CAAC;MAC3C,WAAW,EAAE;QACXI,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,kBAAkB;QAC7BC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,0CAA0ClC,SAAS,2BAA2B;QAC1FmC,OAAO,EAAEhC,SAAS,GAAG,CAAC,GAAG,CAAC;QAC1BiC,UAAU,EAAE;MACd,CAAC;MACD,UAAU,EAAE;QACVR,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBW,MAAM,EAAE,CAAC;QACTP,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,kBAAkB;QAC7BC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,0CAA0ClC,SAAS,2BAA2B;QAC1FmC,OAAO,EAAEhC,SAAS,GAAG,CAAC,GAAG,CAAC;QAC1BiC,UAAU,EAAE;MACd;IACF,CAAE;IAAAvC,QAAA,eAEFF,OAAA,CAACH,SAAS;MACR8C,QAAQ,EAAC,IAAI;MACbpB,EAAE,EAAE;QACFc,KAAK,EAAE,MAAM;QACbO,EAAE,EAAE,MAAM;QACVb,QAAQ,EAAE,UAAU;QACpBc,MAAM,EAAE;MACV,CAAE;MAAA3C,QAAA,eAEFF,OAAA,CAACF,IAAI;QAACgD,EAAE,EAAEtC,SAAU;QAACuC,OAAO,EAAE,IAAK;QAAA7C,QAAA,eACjCF,OAAA,CAACJ,GAAG;UACF2B,EAAE,EAAE;YACFa,SAAS,EAAE5B,SAAS,GAAG,eAAe,GAAG,kBAAkB;YAC3DiC,UAAU,EAAE;UACd,CAAE;UAAAvC,QAAA,EAEDA;QAAQ;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CACN;EAED,IAAI/C,gBAAgB,EAAE;IACpB,oBAAOX,KAAK,CAAC2D,YAAY,CAAChD,gBAAgB,EAAE;MAC1CF,QAAQ,EAAEmB;IACZ,CAAC,CAAC;EACJ;EAEA,OAAOA,cAAc;AACvB,CAAC;AAACd,EAAA,CAtGIN,eAAe;AAAAoD,EAAA,GAAfpD,eAAe;AAwGrB,eAAeA,eAAe;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}