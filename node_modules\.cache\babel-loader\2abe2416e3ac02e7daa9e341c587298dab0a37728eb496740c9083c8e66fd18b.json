{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\HyperspeedBackground.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HyperspeedBackground = ({\n  children,\n  intensity = 0.3,\n  color = '#007AFF',\n  opacity = 0.1\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    let animationId;\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Hyperspeed particles\n    const particles = [];\n    const numParticles = Math.floor(30 * intensity);\n    class Particle {\n      constructor() {\n        this.reset();\n      }\n      reset() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.z = Math.random() * 1000;\n        this.prevX = this.x;\n        this.prevY = this.y;\n      }\n      update() {\n        this.prevX = this.x;\n        this.prevY = this.y;\n        this.z -= 3 * intensity;\n        if (this.z <= 0) {\n          this.reset();\n          return;\n        }\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        this.x = centerX + (this.x - centerX) * (1000 / this.z);\n        this.y = centerY + (this.y - centerY) * (1000 / this.z);\n      }\n      draw() {\n        const opacity = Math.max(0, 1 - this.z / 1000);\n        const size = Math.max(1, (1000 - this.z) / 200);\n        ctx.strokeStyle = `${color}${Math.floor(opacity * 255).toString(16).padStart(2, '0')}`;\n        ctx.lineWidth = size;\n        ctx.beginPath();\n        ctx.moveTo(this.prevX, this.prevY);\n        ctx.lineTo(this.x, this.y);\n        ctx.stroke();\n\n        // Add glow effect\n        ctx.shadowColor = color;\n        ctx.shadowBlur = size * 2;\n        ctx.fillStyle = `${color}${Math.floor(opacity * 128).toString(16).padStart(2, '0')}`;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, size / 2, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.shadowBlur = 0;\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < numParticles; i++) {\n      particles.push(new Particle());\n    }\n    const animate = () => {\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      animationId = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [intensity, color]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: 0,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(HyperspeedBackground, \"X5bd7Q1XXg1keIMflMhOltk4wyU=\");\n_c = HyperspeedBackground;\nexport default HyperspeedBackground;\nvar _c;\n$RefreshReg$(_c, \"HyperspeedBackground\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "jsxDEV", "_jsxDEV", "HyperspeedBackground", "children", "intensity", "color", "opacity", "_s", "canvasRef", "animationRef", "canvas", "current", "ctx", "getContext", "animationId", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "particles", "numParticles", "Math", "floor", "Particle", "constructor", "reset", "x", "random", "y", "z", "prevX", "prevY", "update", "centerX", "centerY", "draw", "max", "size", "strokeStyle", "toString", "padStart", "lineWidth", "beginPath", "moveTo", "lineTo", "stroke", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "fillStyle", "arc", "PI", "fill", "i", "push", "animate", "fillRect", "for<PERSON>ach", "particle", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "sx", "position", "overflow", "ref", "style", "top", "left", "zIndex", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/HyperspeedBackground.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\n\nconst HyperspeedBackground = ({ children, intensity = 0.3, color = '#007AFF', opacity = 0.1 }) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationId;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Hyperspeed particles\n    const particles = [];\n    const numParticles = Math.floor(30 * intensity);\n\n    class Particle {\n      constructor() {\n        this.reset();\n      }\n\n      reset() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.z = Math.random() * 1000;\n        this.prevX = this.x;\n        this.prevY = this.y;\n      }\n\n      update() {\n        this.prevX = this.x;\n        this.prevY = this.y;\n\n        this.z -= 3 * intensity;\n\n        if (this.z <= 0) {\n          this.reset();\n          return;\n        }\n\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n\n        this.x = centerX + (this.x - centerX) * (1000 / this.z);\n        this.y = centerY + (this.y - centerY) * (1000 / this.z);\n      }\n\n      draw() {\n        const opacity = Math.max(0, 1 - this.z / 1000);\n        const size = Math.max(1, (1000 - this.z) / 200);\n\n        ctx.strokeStyle = `${color}${Math.floor(opacity * 255).toString(16).padStart(2, '0')}`;\n        ctx.lineWidth = size;\n        ctx.beginPath();\n        ctx.moveTo(this.prevX, this.prevY);\n        ctx.lineTo(this.x, this.y);\n        ctx.stroke();\n\n        // Add glow effect\n        ctx.shadowColor = color;\n        ctx.shadowBlur = size * 2;\n        ctx.fillStyle = `${color}${Math.floor(opacity * 128).toString(16).padStart(2, '0')}`;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, size / 2, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.shadowBlur = 0;\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < numParticles; i++) {\n      particles.push(new Particle());\n    }\n\n    const animate = () => {\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [intensity, color]);\n\n  return (\n    <Box sx={{ position: 'relative', overflow: 'hidden' }}>\n      <canvas\n        ref={canvasRef}\n        style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          zIndex: 0,\n          pointerEvents: 'none',\n        }}\n      />\n      <Box sx={{ position: 'relative', zIndex: 1 }}>\n        {children}\n      </Box>\n    </Box>\n  );\n};\n\nexport default HyperspeedBackground;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG,GAAG;EAAEC,KAAK,GAAG,SAAS;EAAEC,OAAO,GAAG;AAAI,CAAC,KAAK;EAAAC,EAAA;EAChG,MAAMC,SAAS,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMW,YAAY,GAAGX,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMa,MAAM,GAAGF,SAAS,CAACG,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAIC,WAAW;IAEf,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBL,MAAM,CAACM,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCR,MAAM,CAACS,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,SAAS,GAAG,EAAE;IACpB,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAAC,EAAE,GAAGrB,SAAS,CAAC;IAE/C,MAAMsB,QAAQ,CAAC;MACbC,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACC,KAAK,CAAC,CAAC;MACd;MAEAA,KAAKA,CAAA,EAAG;QACN,IAAI,CAACC,CAAC,GAAGL,IAAI,CAACM,MAAM,CAAC,CAAC,GAAGpB,MAAM,CAACM,KAAK;QACrC,IAAI,CAACe,CAAC,GAAGP,IAAI,CAACM,MAAM,CAAC,CAAC,GAAGpB,MAAM,CAACS,MAAM;QACtC,IAAI,CAACa,CAAC,GAAGR,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,IAAI;QAC7B,IAAI,CAACG,KAAK,GAAG,IAAI,CAACJ,CAAC;QACnB,IAAI,CAACK,KAAK,GAAG,IAAI,CAACH,CAAC;MACrB;MAEAI,MAAMA,CAAA,EAAG;QACP,IAAI,CAACF,KAAK,GAAG,IAAI,CAACJ,CAAC;QACnB,IAAI,CAACK,KAAK,GAAG,IAAI,CAACH,CAAC;QAEnB,IAAI,CAACC,CAAC,IAAI,CAAC,GAAG5B,SAAS;QAEvB,IAAI,IAAI,CAAC4B,CAAC,IAAI,CAAC,EAAE;UACf,IAAI,CAACJ,KAAK,CAAC,CAAC;UACZ;QACF;QAEA,MAAMQ,OAAO,GAAG1B,MAAM,CAACM,KAAK,GAAG,CAAC;QAChC,MAAMqB,OAAO,GAAG3B,MAAM,CAACS,MAAM,GAAG,CAAC;QAEjC,IAAI,CAACU,CAAC,GAAGO,OAAO,GAAG,CAAC,IAAI,CAACP,CAAC,GAAGO,OAAO,KAAK,IAAI,GAAG,IAAI,CAACJ,CAAC,CAAC;QACvD,IAAI,CAACD,CAAC,GAAGM,OAAO,GAAG,CAAC,IAAI,CAACN,CAAC,GAAGM,OAAO,KAAK,IAAI,GAAG,IAAI,CAACL,CAAC,CAAC;MACzD;MAEAM,IAAIA,CAAA,EAAG;QACL,MAAMhC,OAAO,GAAGkB,IAAI,CAACe,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAACP,CAAC,GAAG,IAAI,CAAC;QAC9C,MAAMQ,IAAI,GAAGhB,IAAI,CAACe,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAACP,CAAC,IAAI,GAAG,CAAC;QAE/CpB,GAAG,CAAC6B,WAAW,GAAG,GAAGpC,KAAK,GAAGmB,IAAI,CAACC,KAAK,CAACnB,OAAO,GAAG,GAAG,CAAC,CAACoC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QACtF/B,GAAG,CAACgC,SAAS,GAAGJ,IAAI;QACpB5B,GAAG,CAACiC,SAAS,CAAC,CAAC;QACfjC,GAAG,CAACkC,MAAM,CAAC,IAAI,CAACb,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;QAClCtB,GAAG,CAACmC,MAAM,CAAC,IAAI,CAAClB,CAAC,EAAE,IAAI,CAACE,CAAC,CAAC;QAC1BnB,GAAG,CAACoC,MAAM,CAAC,CAAC;;QAEZ;QACApC,GAAG,CAACqC,WAAW,GAAG5C,KAAK;QACvBO,GAAG,CAACsC,UAAU,GAAGV,IAAI,GAAG,CAAC;QACzB5B,GAAG,CAACuC,SAAS,GAAG,GAAG9C,KAAK,GAAGmB,IAAI,CAACC,KAAK,CAACnB,OAAO,GAAG,GAAG,CAAC,CAACoC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QACpF/B,GAAG,CAACiC,SAAS,CAAC,CAAC;QACfjC,GAAG,CAACwC,GAAG,CAAC,IAAI,CAACvB,CAAC,EAAE,IAAI,CAACE,CAAC,EAAES,IAAI,GAAG,CAAC,EAAE,CAAC,EAAEhB,IAAI,CAAC6B,EAAE,GAAG,CAAC,CAAC;QACjDzC,GAAG,CAAC0C,IAAI,CAAC,CAAC;QACV1C,GAAG,CAACsC,UAAU,GAAG,CAAC;MACpB;IACF;;IAEA;IACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,YAAY,EAAEgC,CAAC,EAAE,EAAE;MACrCjC,SAAS,CAACkC,IAAI,CAAC,IAAI9B,QAAQ,CAAC,CAAC,CAAC;IAChC;IAEA,MAAM+B,OAAO,GAAGA,CAAA,KAAM;MACpB7C,GAAG,CAACuC,SAAS,GAAG,oBAAoB;MACpCvC,GAAG,CAAC8C,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEhD,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACS,MAAM,CAAC;MAE/CG,SAAS,CAACqC,OAAO,CAACC,QAAQ,IAAI;QAC5BA,QAAQ,CAACzB,MAAM,CAAC,CAAC;QACjByB,QAAQ,CAACtB,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;MAEFxB,WAAW,GAAG+C,qBAAqB,CAACJ,OAAO,CAAC;IAC9C,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXxC,MAAM,CAAC6C,mBAAmB,CAAC,QAAQ,EAAE/C,YAAY,CAAC;MAClD,IAAID,WAAW,EAAE;QACfiD,oBAAoB,CAACjD,WAAW,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAACV,SAAS,EAAEC,KAAK,CAAC,CAAC;EAEtB,oBACEJ,OAAA,CAACF,GAAG;IAACiE,EAAE,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAA/D,QAAA,gBACpDF,OAAA;MACEkE,GAAG,EAAE3D,SAAU;MACf4D,KAAK,EAAE;QACLH,QAAQ,EAAE,UAAU;QACpBI,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPtD,KAAK,EAAE,MAAM;QACbG,MAAM,EAAE,MAAM;QACdoD,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE;MACjB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF3E,OAAA,CAACF,GAAG;MAACiE,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEM,MAAM,EAAE;MAAE,CAAE;MAAApE,QAAA,EAC1CA;IAAQ;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrE,EAAA,CA1HIL,oBAAoB;AAAA2E,EAAA,GAApB3E,oBAAoB;AA4H1B,eAAeA,oBAAoB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}