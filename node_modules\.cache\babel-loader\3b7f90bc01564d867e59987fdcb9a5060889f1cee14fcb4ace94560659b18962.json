{"ast": null, "code": "var _jsxFileName = \"D:\\\\Resume-main\\\\src\\\\components\\\\effects\\\\DecryptedText.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Typography } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DecryptedText = ({\n  text,\n  variant = 'h4',\n  className = '',\n  sx = {},\n  speed = 50,\n  characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?',\n  ...props\n}) => {\n  _s();\n  const [displayText, setDisplayText] = useState('');\n  const [isDecrypting, setIsDecrypting] = useState(false);\n  useEffect(() => {\n    if (!text) return;\n    let timeouts = [];\n    setIsDecrypting(true);\n    setDisplayText('');\n\n    // Initial scrambled text\n    const scrambledText = text.split('').map(char => char === ' ' ? ' ' : characters[Math.floor(Math.random() * characters.length)]).join('');\n    setDisplayText(scrambledText);\n\n    // Decrypt each character one by one\n    text.split('').forEach((char, index) => {\n      const timeout = setTimeout(() => {\n        setDisplayText(prev => {\n          const newText = prev.split('');\n\n          // Scramble remaining characters\n          for (let i = index + 1; i < newText.length; i++) {\n            if (text[i] !== ' ') {\n              newText[i] = characters[Math.floor(Math.random() * characters.length)];\n            }\n          }\n\n          // Set the correct character\n          newText[index] = char;\n          return newText.join('');\n        });\n        if (index === text.length - 1) {\n          setIsDecrypting(false);\n        }\n      }, index * speed);\n      timeouts.push(timeout);\n    });\n    return () => {\n      timeouts.forEach(timeout => clearTimeout(timeout));\n    };\n  }, [text, speed, characters]);\n  return /*#__PURE__*/_jsxDEV(Typography, {\n    variant: variant,\n    className: `${className} ${isDecrypting ? 'decrypting' : 'decrypted'}`,\n    sx: {\n      fontFamily: 'monospace',\n      letterSpacing: '0.1em',\n      position: 'relative',\n      '&.decrypting': {\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'linear-gradient(90deg, transparent 0%, rgba(0, 122, 255, 0.3) 50%, transparent 100%)',\n          animation: 'decrypt-scan 2s ease-in-out infinite',\n          pointerEvents: 'none'\n        }\n      },\n      '&.decrypted': {\n        textShadow: '0 0 10px rgba(0, 122, 255, 0.5)'\n      },\n      '@keyframes decrypt-scan': {\n        '0%': {\n          transform: 'translateX(-100%)'\n        },\n        '100%': {\n          transform: 'translateX(100%)'\n        }\n      },\n      ...sx\n    },\n    ...props,\n    children: displayText\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(DecryptedText, \"fcY/RzMO/6FP0zvJJ44f+rKQoyg=\");\n_c = DecryptedText;\nexport default DecryptedText;\nvar _c;\n$RefreshReg$(_c, \"DecryptedText\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Typography", "jsxDEV", "_jsxDEV", "DecryptedText", "text", "variant", "className", "sx", "speed", "characters", "props", "_s", "displayText", "setDisplayText", "isDecrypting", "setIsDecrypting", "timeouts", "scrambledText", "split", "map", "char", "Math", "floor", "random", "length", "join", "for<PERSON>ach", "index", "timeout", "setTimeout", "prev", "newText", "i", "push", "clearTimeout", "fontFamily", "letterSpacing", "position", "content", "top", "left", "right", "bottom", "background", "animation", "pointerEvents", "textShadow", "transform", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Resume-main/src/components/effects/DecryptedText.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Typography } from '@mui/material';\n\nconst DecryptedText = ({ \n  text, \n  variant = 'h4', \n  className = '', \n  sx = {}, \n  speed = 50,\n  characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?',\n  ...props \n}) => {\n  const [displayText, setDisplayText] = useState('');\n  const [isDecrypting, setIsDecrypting] = useState(false);\n\n  useEffect(() => {\n    if (!text) return;\n\n    let timeouts = [];\n    setIsDecrypting(true);\n    setDisplayText('');\n\n    // Initial scrambled text\n    const scrambledText = text\n      .split('')\n      .map(char => char === ' ' ? ' ' : characters[Math.floor(Math.random() * characters.length)])\n      .join('');\n    \n    setDisplayText(scrambledText);\n\n    // Decrypt each character one by one\n    text.split('').forEach((char, index) => {\n      const timeout = setTimeout(() => {\n        setDisplayText(prev => {\n          const newText = prev.split('');\n          \n          // Scramble remaining characters\n          for (let i = index + 1; i < newText.length; i++) {\n            if (text[i] !== ' ') {\n              newText[i] = characters[Math.floor(Math.random() * characters.length)];\n            }\n          }\n          \n          // Set the correct character\n          newText[index] = char;\n          \n          return newText.join('');\n        });\n\n        if (index === text.length - 1) {\n          setIsDecrypting(false);\n        }\n      }, index * speed);\n\n      timeouts.push(timeout);\n    });\n\n    return () => {\n      timeouts.forEach(timeout => clearTimeout(timeout));\n    };\n  }, [text, speed, characters]);\n\n  return (\n    <Typography\n      variant={variant}\n      className={`${className} ${isDecrypting ? 'decrypting' : 'decrypted'}`}\n      sx={{\n        fontFamily: 'monospace',\n        letterSpacing: '0.1em',\n        position: 'relative',\n        '&.decrypting': {\n          '&::after': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'linear-gradient(90deg, transparent 0%, rgba(0, 122, 255, 0.3) 50%, transparent 100%)',\n            animation: 'decrypt-scan 2s ease-in-out infinite',\n            pointerEvents: 'none',\n          }\n        },\n        '&.decrypted': {\n          textShadow: '0 0 10px rgba(0, 122, 255, 0.5)',\n        },\n        '@keyframes decrypt-scan': {\n          '0%': { transform: 'translateX(-100%)' },\n          '100%': { transform: 'translateX(100%)' }\n        },\n        ...sx\n      }}\n      {...props}\n    >\n      {displayText}\n    </Typography>\n  );\n};\n\nexport default DecryptedText;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,aAAa,GAAGA,CAAC;EACrBC,IAAI;EACJC,OAAO,GAAG,IAAI;EACdC,SAAS,GAAG,EAAE;EACdC,EAAE,GAAG,CAAC,CAAC;EACPC,KAAK,GAAG,EAAE;EACVC,UAAU,GAAG,gEAAgE;EAC7E,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACK,IAAI,EAAE;IAEX,IAAIY,QAAQ,GAAG,EAAE;IACjBD,eAAe,CAAC,IAAI,CAAC;IACrBF,cAAc,CAAC,EAAE,CAAC;;IAElB;IACA,MAAMI,aAAa,GAAGb,IAAI,CACvBc,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAACC,IAAI,IAAIA,IAAI,KAAK,GAAG,GAAG,GAAG,GAAGX,UAAU,CAACY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGd,UAAU,CAACe,MAAM,CAAC,CAAC,CAAC,CAC3FC,IAAI,CAAC,EAAE,CAAC;IAEXZ,cAAc,CAACI,aAAa,CAAC;;IAE7B;IACAb,IAAI,CAACc,KAAK,CAAC,EAAE,CAAC,CAACQ,OAAO,CAAC,CAACN,IAAI,EAAEO,KAAK,KAAK;MACtC,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;QAC/BhB,cAAc,CAACiB,IAAI,IAAI;UACrB,MAAMC,OAAO,GAAGD,IAAI,CAACZ,KAAK,CAAC,EAAE,CAAC;;UAE9B;UACA,KAAK,IAAIc,CAAC,GAAGL,KAAK,GAAG,CAAC,EAAEK,CAAC,GAAGD,OAAO,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;YAC/C,IAAI5B,IAAI,CAAC4B,CAAC,CAAC,KAAK,GAAG,EAAE;cACnBD,OAAO,CAACC,CAAC,CAAC,GAAGvB,UAAU,CAACY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGd,UAAU,CAACe,MAAM,CAAC,CAAC;YACxE;UACF;;UAEA;UACAO,OAAO,CAACJ,KAAK,CAAC,GAAGP,IAAI;UAErB,OAAOW,OAAO,CAACN,IAAI,CAAC,EAAE,CAAC;QACzB,CAAC,CAAC;QAEF,IAAIE,KAAK,KAAKvB,IAAI,CAACoB,MAAM,GAAG,CAAC,EAAE;UAC7BT,eAAe,CAAC,KAAK,CAAC;QACxB;MACF,CAAC,EAAEY,KAAK,GAAGnB,KAAK,CAAC;MAEjBQ,QAAQ,CAACiB,IAAI,CAACL,OAAO,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO,MAAM;MACXZ,QAAQ,CAACU,OAAO,CAACE,OAAO,IAAIM,YAAY,CAACN,OAAO,CAAC,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACxB,IAAI,EAAEI,KAAK,EAAEC,UAAU,CAAC,CAAC;EAE7B,oBACEP,OAAA,CAACF,UAAU;IACTK,OAAO,EAAEA,OAAQ;IACjBC,SAAS,EAAE,GAAGA,SAAS,IAAIQ,YAAY,GAAG,YAAY,GAAG,WAAW,EAAG;IACvEP,EAAE,EAAE;MACF4B,UAAU,EAAE,WAAW;MACvBC,aAAa,EAAE,OAAO;MACtBC,QAAQ,EAAE,UAAU;MACpB,cAAc,EAAE;QACd,UAAU,EAAE;UACVC,OAAO,EAAE,IAAI;UACbD,QAAQ,EAAE,UAAU;UACpBE,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,sFAAsF;UAClGC,SAAS,EAAE,sCAAsC;UACjDC,aAAa,EAAE;QACjB;MACF,CAAC;MACD,aAAa,EAAE;QACbC,UAAU,EAAE;MACd,CAAC;MACD,yBAAyB,EAAE;QACzB,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAoB,CAAC;QACxC,MAAM,EAAE;UAAEA,SAAS,EAAE;QAAmB;MAC1C,CAAC;MACD,GAAGxC;IACL,CAAE;IAAA,GACEG,KAAK;IAAAsC,QAAA,EAERpC;EAAW;IAAAqC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEjB,CAAC;AAACzC,EAAA,CA9FIR,aAAa;AAAAkD,EAAA,GAAblD,aAAa;AAgGnB,eAAeA,aAAa;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}